# 🚀 IsotopeAI Subscription Setup Guide

## ❌ **The Problem**
You're getting "Plan not configured with Dodo Payments" because:
1. CORS prevents direct browser API calls to Dodo Payments
2. The subscription plan in your database needs to be linked to a Dodo product
3. Environment variables aren't being read properly

## ✅ **The Solution (Hardcoded Config)**

**NEW APPROACH:** We've moved away from environment variables to a hardcoded configuration file that's easier to manage.

### **Step 1: Check Current Status**
1. Go to: `http://localhost:5173/subscription/upgrade`
2. You'll see setup instructions if configuration is incomplete
3. Check the "Configuration Status" section

### **Step 2: Create Database Plan**
Run in browser console:
```javascript
await dodoSetup.manual()
```

### **Step 3: Create Dodo Product (Manual)**
Since we can't create it via API due to CORS, do this manually:

1. **Go to Dodo Payments Dashboard:**
   - Visit: https://app.dodopayments.com/products
   - Log in with your account

2. **Create New Product:**
   - Click "Create Product"
   - **Name:** `IsotopeAI Premium`
   - **Description:** `Full access to IsotopeAI platform with unlimited AI assistance, productivity tools, analytics, and premium features`
   - **Type:** `Subscription`
   - **Price:** `$6.69 USD`
   - **Billing Interval:** `Yearly`
   - **Tax Category:** `Digital Products`

3. **Copy Product ID:**
   - After creation, copy the product ID (starts with `prod_`)

### **Step 4: Update Configuration File**
1. **Edit the config file:**
   - Open: `src/config/dodoConfig.ts`
   - Find the line: `productId: 'REPLACE_WITH_YOUR_ACTUAL_PRODUCT_ID',`
   - Replace with your actual product ID: `productId: 'prod_your_actual_id_here',`

2. **Save and refresh:**
   - Save the file
   - Refresh your browser page

### **Step 5: Verify Setup**
1. Refresh the page
2. Click "Check Setup" again
3. Should show: "Plan has Dodo Product ID: ✅ Yes"

### **Step 6: Test Subscription Flow**
1. Try clicking "Upgrade Now"
2. Should redirect to Dodo Payments checkout
3. Complete test payment (use test card if in sandbox)

## 🛠️ **Available Console Commands**

```javascript
// Check current setup status
await dodoSetup.check()

// Create database plan if missing
await dodoSetup.manual()

// Link product ID after manual creation
await dodoSetup.updatePlan("prod_xxxxx")
```

## 🔍 **Troubleshooting**

### **"No subscription plans found"**
- Run: `await dodoSetup.manual()`

### **"Plan missing dodo_product_id"**
- Create product manually in Dodo dashboard
- Run: `await dodoSetup.updatePlan("your_product_id")`

### **"CORS error"**
- This is expected - payment APIs block browser requests
- Use manual setup instead

### **"406 error from Supabase"**
- Fixed by changing `.single()` to `.maybeSingle()`
- Should resolve automatically

## 📋 **Environment Variables Needed**

```env
# Dodo Payments Configuration
VITE_DODO_API_KEY=your_api_key_here
VITE_DODO_ENVIRONMENT=sandbox  # or 'production'
VITE_DODO_PRODUCT_ID=prod_xxxxx  # After manual creation
VITE_DODO_WEBHOOK_SECRET=your_webhook_secret  # Optional for now
```

## 🎯 **Expected Result**

After setup, the debug panel should show:
- ✅ Environment variables all set
- ✅ Setup status: Complete
- ✅ Plan has Dodo Product ID: Yes
- ✅ Available Plans: 1

And the upgrade flow should work:
1. Click "Upgrade Now"
2. Redirect to Dodo Payments
3. Complete payment
4. Return to success page
5. User has premium access

## 🚨 **Why CORS Happens**

Payment providers like Dodo Payments block direct browser API calls for security:
- Prevents API key exposure in frontend
- Stops unauthorized API usage
- Forces proper server-side integration

**Normal flow:** Frontend → Your Backend → Payment API
**Our workaround:** Manual product creation + database linking

This is temporary - in production you'd have proper backend endpoints.
