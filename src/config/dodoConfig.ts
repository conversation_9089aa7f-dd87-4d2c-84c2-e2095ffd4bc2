// Dodo Payments Configuration
// Set your product ID directly here instead of using environment variables

export const DODO_CONFIG = {
  // API Configuration
  apiKey: import.meta.env.VITE_DODO_API_KEY || 'RxEJxvOZdrS84hPK.GCSLJo6ewuMh3F6_QqSPcywRzp9YKFvSRk1JyOaF8p-l8Kva',
  environment: (import.meta.env.VITE_DODO_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox',
  webhookSecret: import.meta.env.VITE_DODO_WEBHOOK_SECRET || '',
  
  // Product ID - SET THIS DIRECTLY AFTER CREATING PRODUCT IN DODO DASHBOARD
  productId: 'REPLACE_WITH_YOUR_ACTUAL_PRODUCT_ID', // Replace this with your actual product ID from Dodo Payments
  
  // Product Details (for reference)
  product: {
    name: 'IsotopeAI Premium',
    description: 'Full access to IsotopeAI platform with unlimited AI assistance, productivity tools, analytics, and premium features',
    price: 669, // $6.69 in cents
    currency: 'USD',
    billingInterval: 'year',
  },
  
  // URLs
  baseUrl: 'https://test.dodopayments.com', // Change to 'https://live.dodopayments.com' for production
  returnUrl: `${window.location.origin}/subscription/success`,
  
  // Feature flags
  useHardcodedConfig: true, // Set to false to use environment variables instead
};

// Helper to get the current configuration
export const getDodoConfig = () => {
  if (DODO_CONFIG.useHardcodedConfig) {
    return DODO_CONFIG;
  }
  
  // Fallback to environment variables
  return {
    ...DODO_CONFIG,
    apiKey: import.meta.env.VITE_DODO_API_KEY || DODO_CONFIG.apiKey,
    environment: (import.meta.env.VITE_DODO_ENVIRONMENT as 'sandbox' | 'production') || DODO_CONFIG.environment,
    productId: import.meta.env.VITE_DODO_PRODUCT_ID || DODO_CONFIG.productId,
    webhookSecret: import.meta.env.VITE_DODO_WEBHOOK_SECRET || DODO_CONFIG.webhookSecret,
  };
};

// Validation helper
export const validateDodoConfig = () => {
  const config = getDodoConfig();
  const issues: string[] = [];
  
  if (!config.apiKey || config.apiKey === 'your_api_key_here') {
    issues.push('API Key not set');
  }
  
  if (!config.productId || config.productId === 'REPLACE_WITH_YOUR_ACTUAL_PRODUCT_ID') {
    issues.push('Product ID not set');
  }
  
  if (config.environment !== 'sandbox' && config.environment !== 'production') {
    issues.push('Invalid environment');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    config,
  };
};

// Development helper
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).dodoConfig = {
    get: getDodoConfig,
    validate: validateDodoConfig,
    current: DODO_CONFIG,
  };
  
  console.log('🔧 Dodo Config Tools:');
  console.log('- dodoConfig.get() - Get current config');
  console.log('- dodoConfig.validate() - Check configuration');
  console.log('- dodoConfig.current - View hardcoded config');
}
