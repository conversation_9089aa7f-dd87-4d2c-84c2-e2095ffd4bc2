// Quick fix utility for subscription setup issues
import { supabase } from '@/integrations/supabase/client';
import { simpleDodoPayments } from './dodoPaymentsSimple';

export const checkSetupStatus = async () => {
  console.log('🔍 Checking subscription setup status...');
  
  try {
    // Check if subscription plans exist
    const { data: plans, error: plansError } = await supabase
      .from('subscription_plans')
      .select('*');
    
    if (plansError) {
      console.error('❌ Error fetching subscription plans:', plansError);
      return false;
    }
    
    console.log('📋 Subscription plans in database:', plans);
    
    if (!plans || plans.length === 0) {
      console.log('⚠️ No subscription plans found in database');
      return false;
    }
    
    const premiumPlan = plans.find(plan => plan.name === 'IsotopeAI Premium');
    if (!premiumPlan) {
      console.log('⚠️ IsotopeAI Premium plan not found');
      return false;
    }
    
    if (!premiumPlan.dodo_product_id) {
      console.log('⚠️ Premium plan missing dodo_product_id');
      return false;
    }
    
    console.log('✅ Setup looks good!');
    console.log('Premium plan:', premiumPlan);
    return true;
    
  } catch (error) {
    console.error('❌ Setup check failed:', error);
    return false;
  }
};

export const createMissingPlan = async () => {
  console.log('🔧 Creating missing subscription plan...');
  
  try {
    const { data: existingPlan } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('name', 'IsotopeAI Premium')
      .single();
    
    if (existingPlan) {
      console.log('✅ Plan already exists:', existingPlan);
      return existingPlan;
    }
    
    // Create the plan
    const { data: newPlan, error } = await supabase
      .from('subscription_plans')
      .insert({
        name: 'IsotopeAI Premium',
        description: 'Full access to IsotopeAI platform with unlimited AI assistance, productivity tools, analytics, and premium features',
        price_cents: 669,
        currency: 'USD',
        billing_interval: 'year',
        billing_interval_count: 1,
        features: [
          'Unlimited AI Chat',
          'Advanced Analytics',
          'Productivity Tools',
          'Mock Tests',
          'Group Features',
          'Priority Support',
          'Offline Access'
        ],
        is_active: true,
      })
      .select()
      .single();
    
    if (error) {
      console.error('❌ Error creating plan:', error);
      throw error;
    }
    
    console.log('✅ Plan created successfully:', newPlan);
    return newPlan;
    
  } catch (error) {
    console.error('❌ Plan creation failed:', error);
    throw error;
  }
};

export const linkDodoProduct = async (dodoProductId: string) => {
  console.log('🔗 Linking Dodo product to database plan...');
  
  try {
    const { error } = await supabase
      .from('subscription_plans')
      .update({ 
        dodo_product_id: dodoProductId,
        updated_at: new Date().toISOString()
      })
      .eq('name', 'IsotopeAI Premium');
    
    if (error) {
      console.error('❌ Error linking product:', error);
      throw error;
    }
    
    console.log('✅ Product linked successfully!');
    
  } catch (error) {
    console.error('❌ Product linking failed:', error);
    throw error;
  }
};

export const quickFix = async () => {
  console.log('🚀 Running quick fix for subscription setup...');
  
  try {
    // Step 1: Check current status
    const isSetupOk = await checkSetupStatus();
    if (isSetupOk) {
      console.log('✅ Setup is already working!');
      return;
    }
    
    // Step 2: Create plan if missing
    await createMissingPlan();
    
    // Step 3: Test Dodo connection
    console.log('🔌 Testing Dodo Payments connection...');
    const connectionOk = await simpleDodoPayments.testConnection();
    if (!connectionOk) {
      throw new Error('Dodo Payments connection failed. Check your API key.');
    }
    console.log('✅ Dodo Payments connection successful');
    
    // Step 4: Check if we need to create a product in Dodo
    console.log('🔍 Checking for existing Dodo products...');

    // Create a temporary client to access makeRequest
    const apiKey = import.meta.env.VITE_DODO_API_KEY;
    const environment = import.meta.env.VITE_DODO_ENVIRONMENT as 'sandbox' | 'production';
    const baseUrl = environment === 'production'
      ? 'https://live.dodopayments.com'
      : 'https://test.dodopayments.com';

    const makeRequest = async (endpoint: string, options: RequestInit = {}) => {
      const response = await fetch(`${baseUrl}${endpoint}`, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          ...options.headers,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(`API Error: ${response.status} - ${errorData?.message || response.statusText}`);
      }

      return response.json();
    };

    const products = await makeRequest('/products');
    console.log('Existing products:', products);
    
    let dodoProductId = null;
    
    // Look for existing IsotopeAI product
    const existingProduct = products.find((p: any) => 
      p.name === 'IsotopeAI Premium' || p.name.includes('IsotopeAI')
    );
    
    if (existingProduct) {
      console.log('✅ Found existing Dodo product:', existingProduct);
      dodoProductId = existingProduct.product_id;
    } else {
      console.log('🔧 Creating new product in Dodo Payments...');
      const newProduct = await makeRequest('/products', {
        method: 'POST',
        body: JSON.stringify({
          name: 'IsotopeAI Premium',
          description: 'Full access to IsotopeAI platform with unlimited AI assistance, productivity tools, analytics, and premium features',
          price: {
            type: 'recurring_price',
            currency: 'USD',
            price: 669, // $6.69 in cents
            payment_frequency_count: 1,
            payment_frequency_interval: 'Year',
            subscription_period_count: 1,
            subscription_period_interval: 'Year',
            trial_period_days: 0,
            discount: 0,
            purchasing_power_parity: false,
          },
          tax_category: 'digital_products',
        }),
      });
      console.log('✅ Product created:', newProduct);
      dodoProductId = newProduct.product_id;
    }
    
    // Step 5: Link the product
    if (dodoProductId) {
      await linkDodoProduct(dodoProductId);
    }
    
    // Step 6: Final check
    const finalCheck = await checkSetupStatus();
    if (finalCheck) {
      console.log('🎉 Quick fix completed successfully!');
      console.log('📋 Update your .env file:');
      console.log(`VITE_DODO_PRODUCT_ID=${dodoProductId}`);
    } else {
      console.log('⚠️ Quick fix completed but there may still be issues');
    }
    
  } catch (error) {
    console.error('❌ Quick fix failed:', error);
    throw error;
  }
};

// Development helpers
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).subscriptionFix = {
    check: checkSetupStatus,
    createPlan: createMissingPlan,
    linkProduct: linkDodoProduct,
    quickFix: quickFix,
  };
  
  console.log('🛠️ Subscription fix tools available:');
  console.log('- subscriptionFix.check() - Check setup status');
  console.log('- subscriptionFix.quickFix() - Auto-fix common issues');
  console.log('- subscriptionFix.createPlan() - Create missing plan');
  console.log('- subscriptionFix.linkProduct(id) - Link Dodo product');
}
