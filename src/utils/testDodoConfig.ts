// Test utility to verify Dodo Payments configuration
import { getDodoConfig, validateDodoConfig } from '@/config/dodoConfig';
import { simpleDodoPayments } from './dodoPaymentsSimple';

export const testDodoConfiguration = async () => {
  console.log('🧪 Testing Dodo Payments Configuration...');
  
  // Test 1: Validate configuration
  const validation = validateDodoConfig();
  console.log('📋 Configuration Validation:', validation);
  
  if (!validation.isValid) {
    console.error('❌ Configuration is invalid:', validation.issues);
    return false;
  }
  
  // Test 2: Get current config
  const config = getDodoConfig();
  console.log('⚙️ Current Configuration:');
  console.log('- API Key:', config.apiKey ? '✅ Set' : '❌ Missing');
  console.log('- Environment:', config.environment);
  console.log('- Product ID:', config.productId);
  console.log('- Base URL:', config.baseUrl);
  console.log('- Use Hardcoded Config:', config.useHardcodedConfig);
  
  // Test 3: Test API connection
  console.log('🔗 Testing API Connection...');
  try {
    const connectionTest = await simpleDodoPayments.testConnection();
    if (connectionTest) {
      console.log('✅ API Connection successful');
    } else {
      console.log('❌ API Connection failed');
      return false;
    }
  } catch (error) {
    console.error('❌ API Connection error:', error);
    return false;
  }
  
  // Test 4: Test subscription creation (dry run)
  console.log('🧪 Testing Subscription Creation (dry run)...');
  try {
    // This will test the product ID validation without actually creating a subscription
    const testRequest = {
      customerEmail: '<EMAIL>',
      customerName: 'Test User',
      returnUrl: `${window.location.origin}/subscription/success`,
      metadata: { test: true }
    };
    
    // Just validate the product ID without making the actual request
    const productId = config.productId;
    if (!productId || productId === 'REPLACE_WITH_YOUR_ACTUAL_PRODUCT_ID') {
      throw new Error('Product ID not configured properly');
    }
    
    console.log('✅ Product ID validation passed:', productId);
    console.log('✅ Subscription creation would work with current config');
    
  } catch (error) {
    console.error('❌ Subscription creation test failed:', error);
    return false;
  }
  
  console.log('🎉 All tests passed! Dodo Payments configuration is working correctly.');
  return true;
};

// Development helper
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).testDodoConfig = testDodoConfiguration;
  console.log('🧪 Test utility available: testDodoConfig()');
}
