import { Database } from '@/integrations/supabase/types';

// Types
export interface DodoPaymentsConfig {
  apiKey: string;
  environment: 'sandbox' | 'production';
  webhookSecret: string;
}

export interface CreateSubscriptionRequest {
  productId: string;
  customerId?: string;
  customerEmail: string;
  customerName: string;
  billingAddress: {
    street: string;
    city: string;
    state: string;
    zipcode: string;
    country: string;
  };
  returnUrl?: string;
  metadata?: Record<string, any>;
}

export interface CreateSubscriptionResponse {
  subscription_id: string;
  payment_link: string;
  client_secret: string;
  customer: {
    customer_id: string;
    email: string;
    name: string;
  };
  recurring_pre_tax_amount: number;
}

export interface SubscriptionStatus {
  subscription_id: string;
  status: 'pending' | 'active' | 'on_hold' | 'paused' | 'cancelled' | 'failed' | 'expired';
  current_period_start?: string;
  current_period_end?: string;
  next_billing_date?: string;
  customer: {
    customer_id: string;
    email: string;
    name: string;
  };
}

export interface WebhookEvent {
  business_id: string;
  timestamp: string;
  type: string;
  data: any;
}

// Configuration
const getDodoConfig = (): DodoPaymentsConfig => {
  const apiKey = import.meta.env.VITE_DODO_API_KEY;
  const environment = import.meta.env.VITE_DODO_ENVIRONMENT as 'sandbox' | 'production';
  const webhookSecret = import.meta.env.VITE_DODO_WEBHOOK_SECRET;

  if (!apiKey) {
    throw new Error('VITE_DODO_API_KEY is not configured');
  }

  return {
    apiKey,
    environment: environment || 'sandbox',
    webhookSecret: webhookSecret || '',
  };
};

// API Base URL
const getApiBaseUrl = (environment: 'sandbox' | 'production'): string => {
  return environment === 'production' 
    ? 'https://live.dodopayments.com'
    : 'https://test.dodopayments.com';
};

// API Client
class DodoPaymentsClient {
  private config: DodoPaymentsConfig;
  private baseUrl: string;

  constructor() {
    this.config = getDodoConfig();
    this.baseUrl = getApiBaseUrl(this.config.environment);
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        `Dodo Payments API Error: ${response.status} - ${errorData?.message || response.statusText}`
      );
    }

    return response.json();
  }

  async createSubscription(request: CreateSubscriptionRequest): Promise<CreateSubscriptionResponse> {
    return this.makeRequest<CreateSubscriptionResponse>('/subscriptions', {
      method: 'POST',
      body: JSON.stringify({
        product_id: request.productId,
        customer: request.customerId ? 
          { customer_id: request.customerId } : 
          { 
            email: request.customerEmail,
            name: request.customerName,
          },
        billing: request.billingAddress,
        payment_link: true,
        quantity: 1,
        return_url: request.returnUrl,
        metadata: request.metadata,
      }),
    });
  }

  async getSubscription(subscriptionId: string): Promise<SubscriptionStatus> {
    return this.makeRequest<SubscriptionStatus>(`/subscriptions/${subscriptionId}`);
  }

  async cancelSubscription(subscriptionId: string): Promise<void> {
    await this.makeRequest(`/subscriptions/${subscriptionId}`, {
      method: 'PATCH',
      body: JSON.stringify({
        status: 'cancelled',
      }),
    });
  }

  async updateSubscription(subscriptionId: string, updates: any): Promise<SubscriptionStatus> {
    return this.makeRequest<SubscriptionStatus>(`/subscriptions/${subscriptionId}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    });
  }

  async createCustomer(email: string, name: string): Promise<{ customer_id: string }> {
    return this.makeRequest<{ customer_id: string }>('/customers', {
      method: 'POST',
      body: JSON.stringify({
        email,
        name,
      }),
    });
  }

  async listProducts(): Promise<any[]> {
    return this.makeRequest<any[]>('/products');
  }
}

// Export singleton instance
export const dodoPayments = new DodoPaymentsClient();

// Utility functions
export const formatPrice = (cents: number, currency: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(cents / 100);
};

export const isSubscriptionActive = (status: string): boolean => {
  return ['active', 'trialing'].includes(status.toLowerCase());
};

export const isSubscriptionExpired = (status: string, currentPeriodEnd?: string): boolean => {
  if (status === 'expired' || status === 'cancelled') {
    return true;
  }
  
  if (currentPeriodEnd) {
    return new Date(currentPeriodEnd) < new Date();
  }
  
  return false;
};

// Webhook verification
export const verifyWebhookSignature = (
  payload: string,
  signature: string,
  secret: string
): boolean => {
  // Implement webhook signature verification based on Dodo Payments documentation
  // This is a placeholder - you'll need to implement the actual verification logic
  // based on how Dodo Payments signs their webhooks
  return true;
};

// Error handling
export class DodoPaymentsError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public errorCode?: string
  ) {
    super(message);
    this.name = 'DodoPaymentsError';
  }
}
