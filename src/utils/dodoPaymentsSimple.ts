// Simplified Dodo Payments integration using their default checkout interface
// This approach requires minimal custom code and uses Dodo's hosted checkout

import { getDodoConfig } from '@/config/dodoConfig';

interface CreateSubscriptionRequest {
  productId?: string; // Made optional since we can get it from config
  customerEmail: string;
  customerName: string;
  returnUrl?: string;
  metadata?: Record<string, any>;
}

interface CreateSubscriptionResponse {
  subscription_id: string;
  payment_link: string;
  customer: {
    customer_id: string;
    email: string;
    name: string;
  };
}

// Simple API client for Dodo Payments
class SimpleDodoPaymentsClient {
  private config: ReturnType<typeof getDodoConfig>;

  constructor() {
    this.config = getDodoConfig();

    if (!this.config.apiKey) {
      throw new Error('Dodo Payments API key is not configured');
    }
  }

  private async makeRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.config.baseUrl}${endpoint}`;

    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        `Dodo Payments API Error: ${response.status} - ${errorData?.message || response.statusText}`
      );
    }

    return response.json();
  }

  // Create subscription with minimal required data
  async createSubscription(request: CreateSubscriptionRequest): Promise<CreateSubscriptionResponse> {
    const productId = request.productId || this.config.productId;

    if (!productId || productId === 'REPLACE_WITH_YOUR_ACTUAL_PRODUCT_ID') {
      throw new Error('Product ID not configured. Please set it in src/config/dodoConfig.ts');
    }

    return this.makeRequest<CreateSubscriptionResponse>('/subscriptions', {
      method: 'POST',
      body: JSON.stringify({
        product_id: productId,
        customer: {
          email: request.customerEmail,
          name: request.customerName,
        },
        // Use minimal billing address - Dodo will collect full details in checkout
        billing: {
          street: '123 Main St',
          city: 'City',
          state: 'State',
          zipcode: '12345',
          country: 'US',
        },
        payment_link: true, // This creates a hosted checkout page
        quantity: 1,
        return_url: request.returnUrl || this.config.returnUrl,
        metadata: request.metadata,
      }),
    });
  }

  // Get subscription status
  async getSubscription(subscriptionId: string): Promise<any> {
    return this.makeRequest(`/subscriptions/${subscriptionId}`);
  }

  // Cancel subscription
  async cancelSubscription(subscriptionId: string): Promise<void> {
    await this.makeRequest(`/subscriptions/${subscriptionId}`, {
      method: 'PATCH',
      body: JSON.stringify({
        status: 'cancelled',
      }),
    });
  }

  // Test API connection
  async testConnection(): Promise<boolean> {
    try {
      await this.makeRequest('/products');
      return true;
    } catch (error) {
      console.error('Dodo Payments connection test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const simpleDodoPayments = new SimpleDodoPaymentsClient();

// Utility functions
export const formatPrice = (cents: number, currency: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(cents / 100);
};

export const isSubscriptionActive = (status: string): boolean => {
  return ['active', 'trialing'].includes(status.toLowerCase());
};

// Simple webhook processor for essential events only
export const processWebhookEvent = async (eventType: string, eventData: any) => {
  console.log(`Processing webhook event: ${eventType}`, eventData);
  
  // You can add simple webhook processing here
  // For now, just log the events - the main subscription sync
  // happens when users return to your site
  
  switch (eventType) {
    case 'subscription.active':
      console.log('✅ Subscription activated:', eventData.subscription_id);
      break;
    case 'subscription.cancelled':
      console.log('❌ Subscription cancelled:', eventData.subscription_id);
      break;
    case 'payment.succeeded':
      console.log('💰 Payment succeeded:', eventData.payment_id);
      break;
    case 'payment.failed':
      console.log('💸 Payment failed:', eventData.payment_id);
      break;
    default:
      console.log('ℹ️ Unhandled event type:', eventType);
  }
};

// Error handling
export class SimpleDodoPaymentsError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = 'SimpleDodoPaymentsError';
  }
}

// Setup helper for creating the product and updating database
export const setupProduct = async () => {
  const client = new SimpleDodoPaymentsClient();

  try {
    console.log('🚀 Creating product in Dodo Payments...');

    const product = await client.makeRequest('/products', {
      method: 'POST',
      body: JSON.stringify({
        name: 'IsotopeAI Premium',
        description: 'Full access to IsotopeAI platform with unlimited AI assistance, productivity tools, analytics, and premium features',
        price: {
          type: 'recurring_price',
          currency: 'USD',
          price: 669, // $6.69 in cents
          payment_frequency_count: 1,
          payment_frequency_interval: 'Year',
          subscription_period_count: 1,
          subscription_period_interval: 'Year',
          trial_period_days: 0,
          discount: 0,
          purchasing_power_parity: false,
        },
        tax_category: 'digital_products',
      }),
    });

    console.log('✅ Product created in Dodo Payments:', product);

    // Now update the database with the product ID
    console.log('📝 Updating database with product ID...');

    // Import supabase client
    const { supabase } = await import('@/integrations/supabase/client');

    const { error: updateError } = await supabase
      .from('subscription_plans')
      .update({
        dodo_product_id: product.product_id,
        updated_at: new Date().toISOString()
      })
      .eq('name', 'IsotopeAI Premium');

    if (updateError) {
      console.error('❌ Database update failed:', updateError);
      throw new Error(`Database update failed: ${updateError.message}`);
    }

    console.log('✅ Database updated successfully!');
    console.log('🎉 Setup complete! Product ID:', product.product_id);
    console.log('📋 Next steps:');
    console.log('1. Update your .env file:');
    console.log(`   VITE_DODO_PRODUCT_ID=${product.product_id}`);
    console.log('2. Set up webhooks in Dodo Payments dashboard');
    console.log('3. Test the subscription flow');

    return product;
  } catch (error) {
    console.error('❌ Setup failed:', error);
    throw error;
  }
};

// Development helpers
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).dodoSimple = {
    test: () => simpleDodoPayments.testConnection(),
    setup: setupProduct,
    client: simpleDodoPayments,
  };

  // Also import and expose the fix utilities
  import('./fixSubscriptionSetup').then(({ quickFix, checkSetupStatus }) => {
    (window as any).subscriptionFix = {
      check: checkSetupStatus,
      quickFix: quickFix,
    };
  });
}

/*
🚀 SIMPLIFIED INTEGRATION GUIDE:

1. **No Custom Forms**: Use Dodo's hosted checkout (payment_link: true)
2. **Minimal Data**: Only collect what's absolutely necessary
3. **Default UI**: Let Dodo handle the payment interface
4. **Simple Webhooks**: Basic event logging (optional)
5. **Easy Setup**: One function call to create your product

USAGE:
1. Run: dodoSimple.setup() in browser console
2. Update .env with the returned product_id
3. Users click "Upgrade" → Redirected to Dodo's checkout
4. After payment → Users return to your success page
5. Webhooks (optional) keep your database in sync

BENEFITS:
✅ Less code to maintain
✅ PCI compliance handled by Dodo
✅ Professional checkout experience
✅ Mobile-optimized by default
✅ Automatic payment method detection
✅ Built-in retry logic for failed payments
✅ International payment support
*/
