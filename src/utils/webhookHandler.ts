import { supabase } from '@/integrations/supabase/client';
import { WebhookEvent, verifyWebhookSignature } from './dodoPayments';

// Webhook event handlers
export class WebhookHandler {
  static async handleSubscriptionEvent(event: WebhookEvent): Promise<void> {
    const { type, data } = event;
    
    try {
      switch (type) {
        case 'subscription.active':
          await this.handleSubscriptionActive(data);
          break;
        case 'subscription.on_hold':
          await this.handleSubscriptionOnHold(data);
          break;
        case 'subscription.renewed':
          await this.handleSubscriptionRenewed(data);
          break;
        case 'subscription.cancelled':
          await this.handleSubscriptionCancelled(data);
          break;
        case 'subscription.failed':
          await this.handleSubscriptionFailed(data);
          break;
        case 'subscription.expired':
          await this.handleSubscriptionExpired(data);
          break;
        case 'payment.succeeded':
          await this.handlePaymentSucceeded(data);
          break;
        case 'payment.failed':
          await this.handlePaymentFailed(data);
          break;
        default:
          console.log(`Unhandled webhook event type: ${type}`);
      }
    } catch (error) {
      console.error(`Error handling webhook event ${type}:`, error);
      throw error;
    }
  }

  private static async handleSubscriptionActive(data: any): Promise<void> {
    const { subscription_id, customer, current_period_start, current_period_end, next_billing_date } = data;
    
    // Update subscription status in database
    const { error } = await supabase
      .from('user_subscriptions')
      .update({
        status: 'active',
        current_period_start: current_period_start,
        current_period_end: current_period_end,
        updated_at: new Date().toISOString(),
      })
      .eq('dodo_subscription_id', subscription_id);

    if (error) {
      console.error('Error updating subscription to active:', error);
      throw error;
    }

    console.log(`Subscription ${subscription_id} activated for customer ${customer.email}`);
  }

  private static async handleSubscriptionOnHold(data: any): Promise<void> {
    const { subscription_id } = data;
    
    const { error } = await supabase
      .from('user_subscriptions')
      .update({
        status: 'on_hold',
        updated_at: new Date().toISOString(),
      })
      .eq('dodo_subscription_id', subscription_id);

    if (error) {
      console.error('Error updating subscription to on_hold:', error);
      throw error;
    }

    console.log(`Subscription ${subscription_id} put on hold`);
  }

  private static async handleSubscriptionRenewed(data: any): Promise<void> {
    const { subscription_id, current_period_start, current_period_end, next_billing_date } = data;
    
    const { error } = await supabase
      .from('user_subscriptions')
      .update({
        status: 'active',
        current_period_start: current_period_start,
        current_period_end: current_period_end,
        updated_at: new Date().toISOString(),
      })
      .eq('dodo_subscription_id', subscription_id);

    if (error) {
      console.error('Error updating subscription renewal:', error);
      throw error;
    }

    console.log(`Subscription ${subscription_id} renewed`);
  }

  private static async handleSubscriptionCancelled(data: any): Promise<void> {
    const { subscription_id } = data;
    
    const { error } = await supabase
      .from('user_subscriptions')
      .update({
        status: 'cancelled',
        canceled_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('dodo_subscription_id', subscription_id);

    if (error) {
      console.error('Error updating subscription to cancelled:', error);
      throw error;
    }

    console.log(`Subscription ${subscription_id} cancelled`);
  }

  private static async handleSubscriptionFailed(data: any): Promise<void> {
    const { subscription_id } = data;
    
    const { error } = await supabase
      .from('user_subscriptions')
      .update({
        status: 'failed',
        updated_at: new Date().toISOString(),
      })
      .eq('dodo_subscription_id', subscription_id);

    if (error) {
      console.error('Error updating subscription to failed:', error);
      throw error;
    }

    console.log(`Subscription ${subscription_id} failed`);
  }

  private static async handleSubscriptionExpired(data: any): Promise<void> {
    const { subscription_id } = data;
    
    const { error } = await supabase
      .from('user_subscriptions')
      .update({
        status: 'expired',
        updated_at: new Date().toISOString(),
      })
      .eq('dodo_subscription_id', subscription_id);

    if (error) {
      console.error('Error updating subscription to expired:', error);
      throw error;
    }

    console.log(`Subscription ${subscription_id} expired`);
  }

  private static async handlePaymentSucceeded(data: any): Promise<void> {
    const { payment_id, subscription_id, amount, currency, customer } = data;
    
    // Find the user subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .select('user_id, id')
      .eq('dodo_subscription_id', subscription_id)
      .single();

    if (subscriptionError || !subscription) {
      console.error('Subscription not found for payment:', payment_id);
      return;
    }

    // Record the payment transaction
    const { error: paymentError } = await supabase
      .from('payment_transactions')
      .insert({
        user_id: subscription.user_id,
        subscription_id: subscription.id,
        dodo_payment_id: payment_id,
        dodo_subscription_id: subscription_id,
        amount_cents: amount,
        currency: currency,
        status: 'succeeded',
        transaction_type: 'subscription_payment',
        description: 'Subscription payment',
        processed_at: new Date().toISOString(),
      });

    if (paymentError) {
      console.error('Error recording payment transaction:', paymentError);
      throw paymentError;
    }

    console.log(`Payment ${payment_id} succeeded for subscription ${subscription_id}`);
  }

  private static async handlePaymentFailed(data: any): Promise<void> {
    const { payment_id, subscription_id, amount, currency } = data;
    
    // Find the user subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .select('user_id, id')
      .eq('dodo_subscription_id', subscription_id)
      .single();

    if (subscriptionError || !subscription) {
      console.error('Subscription not found for failed payment:', payment_id);
      return;
    }

    // Record the failed payment transaction
    const { error: paymentError } = await supabase
      .from('payment_transactions')
      .insert({
        user_id: subscription.user_id,
        subscription_id: subscription.id,
        dodo_payment_id: payment_id,
        dodo_subscription_id: subscription_id,
        amount_cents: amount,
        currency: currency,
        status: 'failed',
        transaction_type: 'subscription_payment',
        description: 'Failed subscription payment',
        processed_at: new Date().toISOString(),
      });

    if (paymentError) {
      console.error('Error recording failed payment transaction:', paymentError);
      throw paymentError;
    }

    console.log(`Payment ${payment_id} failed for subscription ${subscription_id}`);
  }

  // Main webhook processor
  static async processWebhook(
    payload: string,
    signature: string,
    secret: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Verify webhook signature
      if (!verifyWebhookSignature(payload, signature, secret)) {
        return { success: false, message: 'Invalid webhook signature' };
      }

      // Parse webhook payload
      const event: WebhookEvent = JSON.parse(payload);
      
      // Handle the event
      await this.handleSubscriptionEvent(event);
      
      return { success: true, message: 'Webhook processed successfully' };
    } catch (error) {
      console.error('Webhook processing error:', error);
      return { 
        success: false, 
        message: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
}
