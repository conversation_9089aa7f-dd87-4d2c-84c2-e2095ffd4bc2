// This utility helps set up the Dodo Payments product for IsotopeAI
// Run this once to create the product in Dodo Payments and update your database

import { supabase } from '@/integrations/supabase/client';

interface DodoProduct {
  product_id: string;
  name: string;
  description: string;
  price: {
    type: 'recurring_price';
    currency: string;
    price: number;
    payment_frequency_count: number;
    payment_frequency_interval: string;
    subscription_period_count: number;
    subscription_period_interval: string;
    trial_period_days: number;
    discount: number;
    purchasing_power_parity: boolean;
  };
  tax_category: string;
}

export async function createDodoProduct(): Promise<string> {
  const apiKey = import.meta.env.VITE_DODO_API_KEY;
  const environment = import.meta.env.VITE_DODO_ENVIRONMENT;
  
  if (!apiKey) {
    throw new Error('VITE_DODO_API_KEY is not configured');
  }

  const baseUrl = environment === 'production' 
    ? 'https://live.dodopayments.com'
    : 'https://test.dodopayments.com';

  const productData = {
    name: 'IsotopeAI Premium',
    description: 'Full access to IsotopeAI platform with unlimited AI assistance, productivity tools, analytics, and premium features',
    price: {
      type: 'recurring_price',
      currency: 'USD',
      price: 669, // $6.69 in cents
      payment_frequency_count: 1,
      payment_frequency_interval: 'Year',
      subscription_period_count: 1,
      subscription_period_interval: 'Year',
      trial_period_days: 0,
      discount: 0,
      purchasing_power_parity: false,
    },
    tax_category: 'digital_products',
  };

  try {
    const response = await fetch(`${baseUrl}/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(productData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(`Failed to create product: ${response.status} - ${errorData?.message || response.statusText}`);
    }

    const product: DodoProduct = await response.json();
    console.log('Product created successfully:', product);

    return product.product_id;
  } catch (error) {
    console.error('Error creating Dodo product:', error);
    throw error;
  }
}

export async function updateSubscriptionPlanWithDodoId(dodoProductId: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('subscription_plans')
      .update({ dodo_product_id: dodoProductId })
      .eq('name', 'IsotopeAI Premium');

    if (error) {
      throw error;
    }

    console.log('Subscription plan updated with Dodo product ID:', dodoProductId);
  } catch (error) {
    console.error('Error updating subscription plan:', error);
    throw error;
  }
}

export async function setupDodoPaymentsIntegration(): Promise<void> {
  try {
    console.log('Setting up Dodo Payments integration...');
    
    // Step 1: Create product in Dodo Payments
    console.log('Creating product in Dodo Payments...');
    const dodoProductId = await createDodoProduct();
    
    // Step 2: Update subscription plan in database
    console.log('Updating subscription plan in database...');
    await updateSubscriptionPlanWithDodoId(dodoProductId);
    
    console.log('✅ Dodo Payments integration setup complete!');
    console.log(`Product ID: ${dodoProductId}`);
    console.log('Please update your .env file with:');
    console.log(`VITE_DODO_PRODUCT_ID=${dodoProductId}`);
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    throw error;
  }
}

// Helper function to test the integration
export async function testDodoPaymentsConnection(): Promise<boolean> {
  const apiKey = import.meta.env.VITE_DODO_API_KEY;
  const environment = import.meta.env.VITE_DODO_ENVIRONMENT;
  
  if (!apiKey) {
    console.error('VITE_DODO_API_KEY is not configured');
    return false;
  }

  const baseUrl = environment === 'production' 
    ? 'https://live.dodopayments.com'
    : 'https://test.dodopayments.com';

  try {
    const response = await fetch(`${baseUrl}/products`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
      },
    });

    if (response.ok) {
      console.log('✅ Dodo Payments connection successful');
      return true;
    } else {
      console.error('❌ Dodo Payments connection failed:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Dodo Payments connection error:', error);
    return false;
  }
}

// Usage instructions
export const SETUP_INSTRUCTIONS = `
🚀 IsotopeAI Subscription Setup Instructions

1. First, test your Dodo Payments connection:
   testDodoPaymentsConnection()

2. Run the setup to create your product:
   setupDodoPaymentsIntegration()

3. Update your .env file with the returned product ID

4. Set up webhooks in Dodo Payments dashboard:
   - Webhook URL: https://your-domain.com/api/webhooks/dodo
   - Events: subscription.*, payment.*

5. Update VITE_DODO_WEBHOOK_SECRET in .env with your webhook secret

6. Deploy your application and test the subscription flow

Note: Make sure your Supabase database has the subscription tables created.
`;

// Console helper for development
if (typeof window !== 'undefined') {
  (window as any).dodoSetup = {
    test: testDodoPaymentsConnection,
    setup: setupDodoPaymentsIntegration,
    instructions: () => console.log(SETUP_INSTRUCTIONS),
  };
}
