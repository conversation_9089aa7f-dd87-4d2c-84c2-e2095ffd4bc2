// Proxy solution for Dodo Payments API calls to avoid CORS issues
// This creates the product via Supabase Edge Functions instead of direct API calls

import { supabase } from '@/integrations/supabase/client';

// Create a Supabase Edge Function to handle Dodo Payments API calls
export const createDodoProductViaProxy = async () => {
  try {
    console.log('🚀 Creating Dodo product via Supabase Edge Function...');
    
    // Call Supabase Edge Function that will make the API call server-side
    const { data, error } = await supabase.functions.invoke('create-dodo-product', {
      body: {
        name: 'IsotopeAI Premium',
        description: 'Full access to IsotopeAI platform with unlimited AI assistance, productivity tools, analytics, and premium features',
        price: {
          type: 'recurring_price',
          currency: 'USD',
          price: 669, // $6.69 in cents
          payment_frequency_count: 1,
          payment_frequency_interval: 'Year',
          subscription_period_count: 1,
          subscription_period_interval: 'Year',
          trial_period_days: 0,
          discount: 0,
          purchasing_power_parity: false,
        },
        tax_category: 'digital_products',
      }
    });

    if (error) {
      throw new Error(`Edge Function error: ${error.message}`);
    }

    console.log('✅ Product created via proxy:', data);
    return data;
  } catch (error) {
    console.error('❌ Proxy product creation failed:', error);
    throw error;
  }
};

// Alternative: Manual setup without API calls
export const manualSetup = async () => {
  console.log('🔧 Running manual setup...');
  
  try {
    // Step 1: Ensure subscription plan exists in database
    const { data: existingPlan, error: checkError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('name', 'IsotopeAI Premium')
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError;
    }

    let plan = existingPlan;

    if (!plan) {
      console.log('📝 Creating subscription plan in database...');
      const { data: newPlan, error: createError } = await supabase
        .from('subscription_plans')
        .insert({
          name: 'IsotopeAI Premium',
          description: 'Full access to IsotopeAI platform with unlimited AI assistance, productivity tools, analytics, and premium features',
          price_cents: 669,
          currency: 'USD',
          billing_interval: 'year',
          billing_interval_count: 1,
          features: [
            'Unlimited AI Chat',
            'Advanced Analytics', 
            'Productivity Tools',
            'Mock Tests',
            'Group Features',
            'Priority Support',
            'Offline Access'
          ],
          is_active: true,
        })
        .select()
        .single();

      if (createError) {
        throw createError;
      }

      plan = newPlan;
      console.log('✅ Plan created:', plan);
    } else {
      console.log('✅ Plan already exists:', plan);
    }

    // Step 2: Check if plan has dodo_product_id
    if (plan.dodo_product_id) {
      console.log('✅ Plan already has Dodo product ID:', plan.dodo_product_id);
      return plan;
    }

    // Step 3: Manual product ID setup
    console.log('⚠️ Plan missing Dodo product ID');
    console.log('📋 Manual setup required:');
    console.log('1. Go to https://app.dodopayments.com/products');
    console.log('2. Create a new product with these details:');
    console.log('   - Name: IsotopeAI Premium');
    console.log('   - Price: $6.69 USD');
    console.log('   - Billing: Yearly');
    console.log('   - Type: Subscription');
    console.log('3. Copy the product ID');
    console.log('4. Update your .env file: VITE_DODO_PRODUCT_ID=your_product_id');
    console.log('5. Run: updatePlanWithProductId("your_product_id")');

    return plan;
  } catch (error) {
    console.error('❌ Manual setup failed:', error);
    throw error;
  }
};

// Update plan with manually created product ID
export const updatePlanWithProductId = async (productId: string) => {
  try {
    console.log('🔗 Updating plan with product ID:', productId);
    
    const { error } = await supabase
      .from('subscription_plans')
      .update({ 
        dodo_product_id: productId,
        updated_at: new Date().toISOString()
      })
      .eq('name', 'IsotopeAI Premium');

    if (error) {
      throw error;
    }

    console.log('✅ Plan updated successfully!');
    
    // Also update .env suggestion
    console.log('📝 Don\'t forget to update your .env file:');
    console.log(`VITE_DODO_PRODUCT_ID=${productId}`);
    
  } catch (error) {
    console.error('❌ Plan update failed:', error);
    throw error;
  }
};

// Check setup status
export const checkSetup = async () => {
  try {
    console.log('🔍 Checking setup status...');
    
    const { data: plans, error } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('name', 'IsotopeAI Premium');

    if (error) {
      throw error;
    }

    if (!plans || plans.length === 0) {
      console.log('❌ No subscription plans found');
      return false;
    }

    const plan = plans[0];
    console.log('📋 Plan found:', plan);

    if (!plan.dodo_product_id) {
      console.log('⚠️ Plan missing dodo_product_id');
      return false;
    }

    console.log('✅ Setup looks good!');
    return true;
  } catch (error) {
    console.error('❌ Setup check failed:', error);
    return false;
  }
};

// Development helpers
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).dodoSetup = {
    manual: manualSetup,
    check: checkSetup,
    updatePlan: updatePlanWithProductId,
    proxy: createDodoProductViaProxy,
  };
  
  console.log('🛠️ Dodo setup tools available:');
  console.log('- dodoSetup.check() - Check current status');
  console.log('- dodoSetup.manual() - Run manual setup');
  console.log('- dodoSetup.updatePlan("product_id") - Link product ID');
  console.log('- dodoSetup.proxy() - Try proxy creation (needs Edge Function)');
}
