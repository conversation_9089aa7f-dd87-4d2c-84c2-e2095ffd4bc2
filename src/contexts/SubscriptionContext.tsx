import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useSupabaseAuth } from './SupabaseAuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';
import { simpleDodoPayments, isSubscriptionActive } from '@/utils/dodoPaymentsSimple';

// Types
type UserSubscription = Database['public']['Tables']['user_subscriptions']['Row'];
type SubscriptionPlan = Database['public']['Tables']['subscription_plans']['Row'];

export interface SubscriptionContextType {
  // Subscription state
  subscription: UserSubscription | null;
  subscriptionPlan: SubscriptionPlan | null;
  isLoading: boolean;
  error: string | null;
  
  // Subscription status
  isActive: boolean;
  isExpired: boolean;
  isPending: boolean;
  isCancelled: boolean;
  
  // Actions
  createSubscription: (planId: string, billingAddress: any) => Promise<string>;
  cancelSubscription: () => Promise<void>;
  refreshSubscription: () => Promise<void>;
  
  // Plan information
  availablePlans: SubscriptionPlan[];
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

interface SubscriptionProviderProps {
  children: ReactNode;
}

export function SubscriptionProvider({ children }: SubscriptionProviderProps) {
  const { user } = useSupabaseAuth();
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [subscriptionPlan, setSubscriptionPlan] = useState<SubscriptionPlan | null>(null);
  const [availablePlans, setAvailablePlans] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Computed subscription status
  const isActive = subscription ? isSubscriptionActive(subscription.status) : false;
  const isExpired = subscription ? ['expired', 'cancelled'].includes(subscription.status) &&
    (subscription.current_period_end ? new Date(subscription.current_period_end) < new Date() : false) : false;
  const isPending = subscription?.status === 'pending' || false;
  const isCancelled = subscription?.status === 'cancelled' || false;

  // Fetch user's subscription
  const fetchSubscription = async () => {
    if (!user) {
      setSubscription(null);
      setSubscriptionPlan(null);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Fetch user's subscription
      const { data: subscriptionData, error: subscriptionError } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle();

      if (subscriptionError && subscriptionError.code !== 'PGRST116') {
        throw subscriptionError;
      }

      if (subscriptionData) {
        setSubscription(subscriptionData);

        // Fetch the subscription plan separately
        if (subscriptionData.subscription_plan_id) {
          const { data: planData, error: planError } = await supabase
            .from('subscription_plans')
            .select('*')
            .eq('id', subscriptionData.subscription_plan_id)
            .single();

          if (!planError && planData) {
            setSubscriptionPlan(planData);
          }
        }

        // Sync with Dodo Payments if we have a subscription ID
        if (subscriptionData.dodo_subscription_id) {
          try {
            const dodoSubscription = await simpleDodoPayments.getSubscription(subscriptionData.dodo_subscription_id);

            // Update local subscription if status differs
            if (dodoSubscription.status !== subscriptionData.status) {
              await updateSubscriptionStatus(subscriptionData.id, dodoSubscription.status);
            }
          } catch (dodoError) {
            console.warn('Failed to sync with Dodo Payments:', dodoError);
          }
        }
      } else {
        setSubscription(null);
        setSubscriptionPlan(null);
      }
    } catch (err) {
      console.error('Error fetching subscription:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch subscription');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch available plans
  const fetchAvailablePlans = async () => {
    try {
      const { data: plans, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('price_cents', { ascending: true });

      if (error) throw error;
      setAvailablePlans(plans || []);
    } catch (err) {
      console.error('Error fetching subscription plans:', err);
    }
  };

  // Update subscription status in database
  const updateSubscriptionStatus = async (subscriptionId: string, status: string) => {
    const { error } = await supabase
      .from('user_subscriptions')
      .update({ 
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', subscriptionId);

    if (error) {
      console.error('Error updating subscription status:', error);
    }
  };

  // Create new subscription
  const createSubscription = async (planId: string, _billingAddress?: any): Promise<string> => {
    if (!user) throw new Error('User not authenticated');

    try {
      setIsLoading(true);
      setError(null);

      // Get the plan details
      const { data: plan, error: planError } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('id', planId)
        .single();

      if (planError || !plan) {
        throw new Error('Subscription plan not found');
      }

      if (!plan.dodo_product_id) {
        throw new Error('Plan not configured with Dodo Payments');
      }

      // Create subscription with Dodo Payments
      const dodoResponse = await simpleDodoPayments.createSubscription({
        productId: plan.dodo_product_id, // This will fallback to config if not set
        customerEmail: user.email || '',
        customerName: user.user_metadata?.full_name || user.email || '',
        returnUrl: `${window.location.origin}/subscription/success`,
        metadata: {
          user_id: user.id,
          plan_id: planId,
        },
      });

      // Create or update subscription record in database
      const subscriptionData = {
        user_id: user.id,
        subscription_plan_id: planId,
        dodo_subscription_id: dodoResponse.subscription_id,
        dodo_customer_id: dodoResponse.customer.customer_id,
        status: 'pending' as const,
        metadata: JSON.parse(JSON.stringify({
          dodo_response: dodoResponse,
        })),
      };

      const { data: newSubscription, error: dbError } = await supabase
        .from('user_subscriptions')
        .upsert(subscriptionData, { onConflict: 'user_id' })
        .select()
        .single();

      if (dbError) throw dbError;

      setSubscription(newSubscription);
      setSubscriptionPlan(plan);

      return dodoResponse.payment_link;
    } catch (err) {
      console.error('Error creating subscription:', err);
      setError(err instanceof Error ? err.message : 'Failed to create subscription');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Cancel subscription
  const cancelSubscription = async () => {
    if (!subscription?.dodo_subscription_id) {
      throw new Error('No active subscription to cancel');
    }

    try {
      setIsLoading(true);
      setError(null);

      // Cancel with Dodo Payments
      await simpleDodoPayments.cancelSubscription(subscription.dodo_subscription_id);

      // Update local status
      const { error } = await supabase
        .from('user_subscriptions')
        .update({ 
          status: 'cancelled',
          canceled_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', subscription.id);

      if (error) throw error;

      // Refresh subscription data
      await fetchSubscription();
    } catch (err) {
      console.error('Error cancelling subscription:', err);
      setError(err instanceof Error ? err.message : 'Failed to cancel subscription');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh subscription data
  const refreshSubscription = async () => {
    await fetchSubscription();
  };

  // Initialize data on mount and user change
  useEffect(() => {
    fetchSubscription();
    fetchAvailablePlans();
  }, [user]);

  // Set up real-time subscription updates
  useEffect(() => {
    if (!user) return;

    const channel = supabase
      .channel('subscription-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_subscriptions',
          filter: `user_id=eq.${user.id}`,
        },
        () => {
          fetchSubscription();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user]);

  const value: SubscriptionContextType = {
    subscription,
    subscriptionPlan,
    isLoading,
    error,
    isActive,
    isExpired,
    isPending,
    isCancelled,
    createSubscription,
    cancelSubscription,
    refreshSubscription,
    availablePlans,
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
}

export function useSubscription() {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
}
