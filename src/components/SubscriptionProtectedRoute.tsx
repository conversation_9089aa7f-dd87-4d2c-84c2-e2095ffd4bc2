import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { SubscriptionUpgradePrompt } from './subscription/SubscriptionUpgradePrompt';

interface SubscriptionProtectedRouteProps {
  children: React.ReactNode;
  fallbackPath?: string;
  showUpgradePrompt?: boolean;
}

export const SubscriptionProtectedRoute: React.FC<SubscriptionProtectedRouteProps> = ({
  children,
  fallbackPath = '/subscription/upgrade',
  showUpgradePrompt = true,
}) => {
  const { user, loading: authLoading } = useSupabaseAuth();
  const { isActive, isLoading: subscriptionLoading, subscription } = useSubscription();
  const location = useLocation();

  // Show loading while checking auth and subscription
  if (authLoading || subscriptionLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" state={{ from: location.pathname }} replace />;
  }

  // Check if user has an active subscription
  if (!isActive) {
    // If user wants to show upgrade prompt instead of redirect
    if (showUpgradePrompt) {
      return (
        <SubscriptionUpgradePrompt 
          subscription={subscription}
          onUpgrade={() => {
            // Redirect to upgrade page
            window.location.href = fallbackPath;
          }}
        />
      );
    }

    // Redirect to upgrade page
    return <Navigate to={fallbackPath} state={{ from: location.pathname }} replace />;
  }

  // User has active subscription, render children
  return <>{children}</>;
};

// Higher-order component for easier usage
export const withSubscriptionProtection = <P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    fallbackPath?: string;
    showUpgradePrompt?: boolean;
  }
) => {
  return (props: P) => (
    <SubscriptionProtectedRoute {...options}>
      <Component {...props} />
    </SubscriptionProtectedRoute>
  );
};

// Hook to check subscription status in components
export const useSubscriptionAccess = () => {
  const { isActive, isExpired, isPending, subscription } = useSubscription();
  
  return {
    hasAccess: isActive,
    isExpired,
    isPending,
    subscription,
    needsUpgrade: !isActive && !isPending,
  };
};
