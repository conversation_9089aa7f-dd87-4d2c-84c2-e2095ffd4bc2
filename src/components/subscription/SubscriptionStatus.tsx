import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Crown, AlertTriangle, CheckCircle, Clock } from 'lucide-react';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { formatPrice } from '@/utils/dodoPaymentsSimple';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface SubscriptionStatusProps {
  variant?: 'header' | 'sidebar' | 'compact';
  className?: string;
}

export const SubscriptionStatus: React.FC<SubscriptionStatusProps> = ({
  variant = 'header',
  className = '',
}) => {
  const navigate = useNavigate();
  const { 
    subscription, 
    subscriptionPlan, 
    isActive, 
    isExpired, 
    isPending, 
    isCancelled,
    isLoading 
  } = useSubscription();

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-primary"></div>
        <span className="text-sm">Loading...</span>
      </div>
    );
  }

  const getStatusInfo = () => {
    if (isActive) {
      return {
        icon: <CheckCircle className="h-4 w-4 text-green-500" />,
        badge: <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Premium</Badge>,
        text: 'Premium Active',
        color: 'text-green-600 dark:text-green-400',
      };
    }
    
    if (isPending) {
      return {
        icon: <Clock className="h-4 w-4 text-yellow-500" />,
        badge: <Badge variant="outline">Pending</Badge>,
        text: 'Payment Pending',
        color: 'text-yellow-600 dark:text-yellow-400',
      };
    }
    
    if (isExpired || isCancelled) {
      return {
        icon: <AlertTriangle className="h-4 w-4 text-red-500" />,
        badge: <Badge variant="destructive">{isExpired ? 'Expired' : 'Cancelled'}</Badge>,
        text: isExpired ? 'Subscription Expired' : 'Subscription Cancelled',
        color: 'text-red-600 dark:text-red-400',
      };
    }
    
    return {
      icon: <Crown className="h-4 w-4 text-gray-500" />,
      badge: <Badge variant="secondary">Free</Badge>,
      text: 'Free Plan',
      color: 'text-gray-600 dark:text-gray-400',
    };
  };

  const statusInfo = getStatusInfo();

  if (variant === 'compact') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {statusInfo.icon}
        {statusInfo.badge}
      </div>
    );
  }

  if (variant === 'sidebar') {
    return (
      <div className={`p-3 rounded-lg border bg-card ${className}`}>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            {statusInfo.icon}
            <span className={`text-sm font-medium ${statusInfo.color}`}>
              {statusInfo.text}
            </span>
          </div>
          {statusInfo.badge}
        </div>
        
        {subscriptionPlan && isActive && (
          <div className="text-xs text-muted-foreground">
            {formatPrice(subscriptionPlan.price_cents, subscriptionPlan.currency)}/{subscriptionPlan.billing_interval}
          </div>
        )}
        
        {subscription?.current_period_end && isActive && (
          <div className="text-xs text-muted-foreground mt-1">
            Renews {new Date(subscription.current_period_end).toLocaleDateString()}
          </div>
        )}
        
        <div className="mt-3 space-y-2">
          {!isActive && (
            <Button
              onClick={() => navigate('/subscription/upgrade')}
              size="sm"
              className="w-full"
            >
              <Crown className="h-3 w-3 mr-1" />
              Upgrade
            </Button>
          )}
          
          {isActive && (
            <Button
              onClick={() => navigate('/subscription/manage')}
              variant="outline"
              size="sm"
              className="w-full"
            >
              Manage
            </Button>
          )}
        </div>
      </div>
    );
  }

  // Header variant (default)
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className={`flex items-center gap-2 ${className}`}>
          {statusInfo.icon}
          <span className="hidden sm:inline">{statusInfo.text}</span>
          {statusInfo.badge}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        <div className="p-3">
          <div className="flex items-center justify-between mb-2">
            <span className="font-medium">Subscription Status</span>
            {statusInfo.badge}
          </div>
          
          {subscriptionPlan && isActive && (
            <>
              <div className="text-sm text-muted-foreground mb-1">
                {subscriptionPlan.name}
              </div>
              <div className="text-sm font-medium">
                {formatPrice(subscriptionPlan.price_cents, subscriptionPlan.currency)}/{subscriptionPlan.billing_interval}
              </div>
            </>
          )}
          
          {subscription?.current_period_end && isActive && (
            <div className="text-xs text-muted-foreground mt-2">
              Renews on {new Date(subscription.current_period_end).toLocaleDateString()}
            </div>
          )}
          
          {(isExpired || isCancelled) && (
            <div className="text-sm text-muted-foreground mt-1">
              {isExpired 
                ? 'Your subscription has expired. Renew to continue using premium features.'
                : 'Your subscription was cancelled. You can reactivate it anytime.'
              }
            </div>
          )}
          
          {isPending && (
            <div className="text-sm text-muted-foreground mt-1">
              Your payment is being processed. This usually takes a few minutes.
            </div>
          )}
        </div>
        
        <DropdownMenuSeparator />
        
        {!isActive && (
          <DropdownMenuItem onClick={() => navigate('/subscription/upgrade')}>
            <Crown className="h-4 w-4 mr-2" />
            {isExpired || isCancelled ? 'Renew Subscription' : 'Upgrade to Premium'}
          </DropdownMenuItem>
        )}
        
        {isActive && (
          <DropdownMenuItem onClick={() => navigate('/subscription/manage')}>
            <Crown className="h-4 w-4 mr-2" />
            Manage Subscription
          </DropdownMenuItem>
        )}
        
        <DropdownMenuItem onClick={() => navigate('/subscription/upgrade')}>
          <Crown className="h-4 w-4 mr-2" />
          View Plans
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
