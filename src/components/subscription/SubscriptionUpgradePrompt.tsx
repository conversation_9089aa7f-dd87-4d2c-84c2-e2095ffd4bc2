import React from 'react';
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Crown, Star, Zap, Shield, Users, BarChart3, Clock, CheckCircle } from 'lucide-react';
import { Database } from '@/integrations/supabase/types';
import { formatPrice } from '@/utils/dodoPaymentsSimple';

type UserSubscription = Database['public']['Tables']['user_subscriptions']['Row'];

interface SubscriptionUpgradePromptProps {
  subscription: UserSubscription | null;
  onUpgrade: () => void;
  className?: string;
}

export const SubscriptionUpgradePrompt: React.FC<SubscriptionUpgradePromptProps> = ({
  subscription,
  onUpgrade,
  className = '',
}) => {
  const features = [
    {
      icon: <Zap className="h-5 w-5" />,
      title: 'Unlimited AI Chat',
      description: 'Ask unlimited questions to our AI tutor',
    },
    {
      icon: <BarChart3 className="h-5 w-5" />,
      title: 'Advanced Analytics',
      description: 'Track your study progress with detailed insights',
    },
    {
      icon: <Clock className="h-5 w-5" />,
      title: 'Productivity Tools',
      description: 'Pomodoro timer, study sessions, and task management',
    },
    {
      icon: <Users className="h-5 w-5" />,
      title: 'Group Features',
      description: 'Collaborate with classmates and join study groups',
    },
    {
      icon: <CheckCircle className="h-5 w-5" />,
      title: 'Mock Tests',
      description: 'Practice with unlimited mock tests and analysis',
    },
    {
      icon: <Shield className="h-5 w-5" />,
      title: 'Priority Support',
      description: 'Get faster help when you need it',
    },
  ];

  const getStatusMessage = () => {
    if (!subscription) {
      return {
        title: 'Unlock IsotopeAI Premium',
        description: 'Get unlimited access to all features and accelerate your learning journey.',
        badge: null,
      };
    }

    switch (subscription.status) {
      case 'expired':
        return {
          title: 'Your Subscription Has Expired',
          description: 'Renew your subscription to continue accessing premium features.',
          badge: <Badge variant="destructive">Expired</Badge>,
        };
      case 'cancelled':
        return {
          title: 'Subscription Cancelled',
          description: 'Reactivate your subscription to regain access to premium features.',
          badge: <Badge variant="secondary">Cancelled</Badge>,
        };
      case 'on_hold':
        return {
          title: 'Subscription On Hold',
          description: 'There was an issue with your payment. Please update your billing information.',
          badge: <Badge variant="outline">On Hold</Badge>,
        };
      default:
        return {
          title: 'Upgrade to Premium',
          description: 'Unlock all features and get the most out of IsotopeAI.',
          badge: null,
        };
    }
  };

  const statusInfo = getStatusMessage();

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4 ${className}`}>
      <div className="max-w-4xl w-full">
        <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
          <CardHeader className="text-center pb-6">
            <div className="flex items-center justify-center mb-4">
              <div className="relative">
                <Crown className="h-16 w-16 text-yellow-500" />
                <Star className="h-6 w-6 text-yellow-400 absolute -top-1 -right-1" />
              </div>
            </div>
            
            <div className="flex items-center justify-center gap-2 mb-2">
              <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {statusInfo.title}
              </CardTitle>
              {statusInfo.badge}
            </div>
            
            <CardDescription className="text-lg text-gray-600 dark:text-gray-300">
              {statusInfo.description}
            </CardDescription>

            <div className="mt-6 p-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg text-white">
              <div className="text-2xl font-bold">
                {formatPrice(669)} / year
              </div>
              <div className="text-sm opacity-90">
                Just {formatPrice(56)} per month
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="grid md:grid-cols-2 gap-4">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="flex items-start gap-3 p-4 rounded-lg bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <div className="text-blue-600 dark:text-blue-400 mt-0.5">
                    {feature.icon}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {feature.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {feature.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center space-y-4 pt-6 border-t">
              <Button
                onClick={onUpgrade}
                size="lg"
                className="w-full md:w-auto px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Crown className="h-5 w-5 mr-2" />
                {subscription?.status === 'expired' || subscription?.status === 'cancelled' 
                  ? 'Renew Subscription' 
                  : 'Upgrade to Premium'
                }
              </Button>
              
              <p className="text-sm text-gray-500 dark:text-gray-400">
                30-day money-back guarantee • Cancel anytime
              </p>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <div className="flex items-center gap-2 text-green-800 dark:text-green-200">
                <CheckCircle className="h-5 w-5" />
                <span className="font-semibold">Special Launch Offer</span>
              </div>
              <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                Get your first year for just $6.69 - that's 90% off the regular price!
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
