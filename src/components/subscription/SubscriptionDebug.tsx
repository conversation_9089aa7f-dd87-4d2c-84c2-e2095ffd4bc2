import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import {
  simpleDodoPayments,
  setupProduct
} from '@/utils/dodoPaymentsSimple';
import { quickFix, checkSetupStatus } from '@/utils/fixSubscriptionSetup';
import { manualSetup, checkSetup, updatePlanWithProductId } from '@/utils/dodoPaymentsProxy';
import { validateDodoConfig } from '@/config/dodoConfig';
import { 
  Code, 
  Database, 
  Settings, 
  TestTube, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';

interface SubscriptionDebugProps {
  className?: string;
}

export const SubscriptionDebug: React.FC<SubscriptionDebugProps> = ({ className = '' }) => {
  const { toast } = useToast();
  const { user } = useSupabaseAuth();
  const { 
    subscription, 
    subscriptionPlan, 
    availablePlans,
    isActive, 
    isExpired, 
    isPending, 
    isCancelled,
    isLoading,
    refreshSubscription 
  } = useSubscription();
  
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isSettingUp, setIsSettingUp] = useState(false);
  const [isFixing, setIsFixing] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'success' | 'failed'>('unknown');

  const handleTestConnection = async () => {
    setIsTestingConnection(true);
    try {
      // Test database connection instead of direct API (to avoid CORS)
      const success = await checkSetup();
      setConnectionStatus(success ? 'success' : 'failed');
      toast({
        title: success ? 'Setup Complete' : 'Setup Needed',
        description: success
          ? 'Database and plan configuration looks good'
          : 'Database setup needs attention',
        variant: success ? 'default' : 'destructive',
      });
    } catch (error) {
      setConnectionStatus('failed');
      toast({
        title: 'Check Failed',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleSetup = async () => {
    setIsSettingUp(true);
    try {
      await manualSetup();
      toast({
        title: 'Manual Setup Complete',
        description: 'Check console for next steps',
      });
      await refreshSubscription();
    } catch (error) {
      toast({
        title: 'Setup Failed',
        description: error instanceof Error ? error.message : 'Setup failed',
        variant: 'destructive',
      });
    } finally {
      setIsSettingUp(false);
    }
  };

  const handleQuickFix = async () => {
    setIsFixing(true);
    try {
      await quickFix();
      toast({
        title: 'Quick Fix Complete',
        description: 'Subscription setup has been fixed successfully',
      });
      await refreshSubscription();
    } catch (error) {
      toast({
        title: 'Quick Fix Failed',
        description: error instanceof Error ? error.message : 'Quick fix failed',
        variant: 'destructive',
      });
    } finally {
      setIsFixing(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const configValidation = validateDodoConfig();
  const envVars = {
    'API Key': configValidation.config.apiKey && configValidation.config.apiKey !== 'your_api_key_here' ? '✅ Set' : '❌ Missing',
    'Environment': configValidation.config.environment || '❌ Missing',
    'Product ID': configValidation.config.productId && configValidation.config.productId !== 'REPLACE_WITH_YOUR_ACTUAL_PRODUCT_ID' ? '✅ Set' : '❌ Missing',
    'Base URL': configValidation.config.baseUrl || '❌ Missing',
  };

  if (process.env.NODE_ENV === 'production') {
    return null; // Don't show debug component in production
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Subscription Debug Panel
          </CardTitle>
          <CardDescription>
            Development tools for testing and debugging the subscription system
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Configuration Status */}
          <div>
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Code className="h-4 w-4" />
              Configuration Status
            </h3>
            {!configValidation.isValid && (
              <div className="mb-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <p className="text-red-800 dark:text-red-200 text-sm font-semibold mb-1">Configuration Issues:</p>
                <ul className="text-red-700 dark:text-red-300 text-sm">
                  {configValidation.issues.map((issue, index) => (
                    <li key={index}>• {issue}</li>
                  ))}
                </ul>
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
              {Object.entries(envVars).map(([key, value]) => (
                <div key={key} className="flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                  <span className="font-mono">{key}</span>
                  <span>{value}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Connection Test */}
          <div>
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <TestTube className="h-4 w-4" />
              Setup Status Check
            </h3>
            <div className="flex items-center gap-3">
              <Button
                onClick={handleTestConnection}
                disabled={isTestingConnection}
                variant="outline"
                size="sm"
              >
                {isTestingConnection ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Testing...
                  </>
                ) : (
                  <>
                    <TestTube className="h-4 w-4 mr-2" />
                    Check Setup
                  </>
                )}
              </Button>
              {connectionStatus !== 'unknown' && (
                <div className="flex items-center gap-2">
                  {getStatusIcon(connectionStatus)}
                  <span className="text-sm">
                    {connectionStatus === 'success' ? 'Connected' : 'Failed'}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Setup */}
          <div>
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Database className="h-4 w-4" />
              Integration Setup
            </h3>
            <div className="space-y-3">
              <div className="flex gap-2">
                <Button
                  onClick={handleQuickFix}
                  disabled={isFixing}
                  size="sm"
                  variant="default"
                >
                  {isFixing ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Fixing...
                    </>
                  ) : (
                    '🔧 Quick Fix'
                  )}
                </Button>
                <Button
                  onClick={handleSetup}
                  disabled={isSettingUp}
                  size="sm"
                  variant="outline"
                >
                  {isSettingUp ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Setting up...
                    </>
                  ) : (
                    'Manual Setup'
                  )}
                </Button>
              </div>
              <Textarea
                value="HARDCODED CONFIG SETUP:\n1. Click 'Manual Setup' to create database plan\n2. Go to app.dodopayments.com/products\n3. Create product: IsotopeAI Premium, $6.69/year\n4. Copy product ID\n5. Edit src/config/dodoConfig.ts\n6. Replace 'REPLACE_WITH_YOUR_ACTUAL_PRODUCT_ID'\n7. Refresh page and test upgrade"
                readOnly
                className="text-xs font-mono h-32"
                placeholder="Setup instructions will appear here..."
              />
            </div>
          </div>

          {/* Current State */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Current Subscription State</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>User ID:</span>
                <span className="font-mono">{user?.id || 'Not logged in'}</span>
              </div>
              <div className="flex justify-between">
                <span>Subscription Status:</span>
                <Badge variant={isActive ? 'default' : 'secondary'}>
                  {subscription?.status || 'None'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Plan:</span>
                <span>{subscriptionPlan?.name || 'None'}</span>
              </div>
              <div className="flex justify-between">
                <span>Available Plans:</span>
                <span>{availablePlans.length}</span>
              </div>
              <div className="flex justify-between">
                <span>Plan has Dodo Product ID:</span>
                <span>{subscriptionPlan?.dodo_product_id ? '✅ Yes' : '❌ No'}</span>
              </div>
              <div className="flex justify-between">
                <span>Dodo Product ID:</span>
                <span className="font-mono text-xs">
                  {subscriptionPlan?.dodo_product_id || 'None'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Dodo Subscription ID:</span>
                <span className="font-mono text-xs">
                  {subscription?.dodo_subscription_id || 'None'}
                </span>
              </div>
            </div>
          </div>

          {/* State Flags */}
          <div>
            <h3 className="text-lg font-semibold mb-3">State Flags</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {[
                { label: 'Loading', value: isLoading },
                { label: 'Active', value: isActive },
                { label: 'Expired', value: isExpired },
                { label: 'Pending', value: isPending },
                { label: 'Cancelled', value: isCancelled },
              ].map(({ label, value }) => (
                <div key={label} className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                  {value ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-gray-400" />
                  )}
                  <span className="text-sm">{label}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2 pt-4 border-t">
            <Button
              onClick={refreshSubscription}
              variant="outline"
              size="sm"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Data
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
