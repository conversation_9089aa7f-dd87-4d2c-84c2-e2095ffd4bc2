import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, Copy, CheckCircle } from 'lucide-react';
import { validateDodoConfig } from '@/config/dodoConfig';

export const SetupInstructions: React.FC = () => {
  const configValidation = validateDodoConfig();

  if (configValidation.isValid) {
    return (
      <Alert className="bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800 dark:text-green-200">
          ✅ Configuration is complete! You can now test the subscription flow.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className="border-orange-200 dark:border-orange-800">
      <CardHeader>
        <CardTitle className="text-orange-800 dark:text-orange-200">
          🔧 Setup Required
        </CardTitle>
        <CardDescription>
          Follow these steps to complete your Dodo Payments integration
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Issues */}
        <div className="space-y-2">
          <h4 className="font-semibold text-red-800 dark:text-red-200">Issues to fix:</h4>
          {configValidation.issues.map((issue, index) => (
            <div key={index} className="flex items-center gap-2">
              <Badge variant="destructive" className="text-xs">!</Badge>
              <span className="text-sm">{issue}</span>
            </div>
          ))}
        </div>

        {/* Step-by-step instructions */}
        <div className="space-y-4 pt-4 border-t">
          <h4 className="font-semibold">Setup Steps:</h4>
          
          {/* Step 1: Database */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge>1</Badge>
              <span className="font-medium">Create Database Plan</span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300 ml-8">
              Run in browser console: <code className="bg-gray-100 dark:bg-gray-800 px-1 rounded">await dodoSetup.manual()</code>
            </p>
          </div>

          {/* Step 2: Dodo Product */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge>2</Badge>
              <span className="font-medium">Create Dodo Product</span>
              <a 
                href="https://app.dodopayments.com/products" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-700 dark:text-blue-400"
              >
                <ExternalLink className="h-4 w-4" />
              </a>
            </div>
            <div className="ml-8 space-y-1 text-sm text-gray-600 dark:text-gray-300">
              <p>• Go to Dodo Payments dashboard</p>
              <p>• Create new product: "IsotopeAI Premium"</p>
              <p>• Price: $6.69 USD, Yearly subscription</p>
              <p>• Copy the product ID (starts with "prod_")</p>
            </div>
          </div>

          {/* Step 3: Update Config */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge>3</Badge>
              <span className="font-medium">Update Configuration</span>
            </div>
            <div className="ml-8 space-y-2">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Edit <code className="bg-gray-100 dark:bg-gray-800 px-1 rounded">src/config/dodoConfig.ts</code>
              </p>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs font-mono">Replace this line:</span>
                  <Copy className="h-4 w-4 text-gray-400" />
                </div>
                <code className="text-xs block">
                  productId: 'REPLACE_WITH_YOUR_ACTUAL_PRODUCT_ID',
                </code>
                <div className="mt-2 text-xs text-gray-500">
                  With your actual product ID from step 2
                </div>
              </div>
            </div>
          </div>

          {/* Step 4: Test */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge>4</Badge>
              <span className="font-medium">Test Integration</span>
            </div>
            <div className="ml-8 space-y-1 text-sm text-gray-600 dark:text-gray-300">
              <p>• Refresh this page</p>
              <p>• Click "Upgrade Now" button</p>
              <p>• Should redirect to Dodo Payments checkout</p>
            </div>
          </div>
        </div>

        {/* Quick commands */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
          <h5 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Quick Commands:</h5>
          <div className="space-y-1 text-sm font-mono">
            <div>dodoSetup.manual() <span className="text-gray-500">// Create database plan</span></div>
            <div>dodoConfig.validate() <span className="text-gray-500">// Check configuration</span></div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
