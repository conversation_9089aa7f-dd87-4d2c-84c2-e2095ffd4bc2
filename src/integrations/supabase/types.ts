export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      aiChats: {
        Row: {
          comments: Json | null
          comments_count: number | null
          created_at_ts: string | null
          createdAt: Json | null
          createdBy: string | null
          id: string
          isPinned: boolean | null
          isPublic: boolean | null
          isStarred: boolean | null
          messages: Json | null
          preview: string | null
          slug: string | null
          status: string | null
          tags: string[] | null
          title: string | null
          updated_at_ts: string | null
          updatedAt: Json | null
          userId: string
          viewCount: number | null
        }
        Insert: {
          comments?: Json | null
          comments_count?: number | null
          created_at_ts?: string | null
          createdAt?: Json | null
          createdBy?: string | null
          id: string
          isPinned?: boolean | null
          isPublic?: boolean | null
          isStarred?: boolean | null
          messages?: Json | null
          preview?: string | null
          slug?: string | null
          status?: string | null
          tags?: string[] | null
          title?: string | null
          updated_at_ts?: string | null
          updatedAt?: Json | null
          userId: string
          viewCount?: number | null
        }
        Update: {
          comments?: Json | null
          comments_count?: number | null
          created_at_ts?: string | null
          createdAt?: Json | null
          createdBy?: string | null
          id?: string
          isPinned?: boolean | null
          isPublic?: boolean | null
          isStarred?: boolean | null
          messages?: Json | null
          preview?: string | null
          slug?: string | null
          status?: string | null
          tags?: string[] | null
          title?: string | null
          updated_at_ts?: string | null
          updatedAt?: Json | null
          userId?: string
          viewCount?: number | null
        }
        Relationships: []
      }
      groups: {
        Row: {
          id: string
          name: string
          description: string | null
          members: string[] | null
          createdBy: string
          createdAt: string | null
          isPublic: boolean | null
          inviteCode: string | null
          owner_id: string | null
          last_message: Json | null
          last_activity: string | null
          updated_at: string | null
          invite_code: string | null
        }
        Insert: {
          id: string
          name: string
          description?: string | null
          members?: string[] | null
          createdBy: string
          createdAt?: string | null
          isPublic?: boolean | null
          inviteCode?: string | null
          owner_id?: string | null
          last_message?: Json | null
          last_activity?: string | null
          updated_at?: string | null
          invite_code?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          members?: string[] | null
          createdBy?: string
          createdAt?: string | null
          isPublic?: boolean | null
          inviteCode?: string | null
          owner_id?: string | null
          last_message?: Json | null
          last_activity?: string | null
          updated_at?: string | null
          invite_code?: string | null
        }
        Relationships: []
      }
      mock_tests: {
        Row: {
          created_at: string | null
          id: string
          name: string
          notes: string | null
          subject_marks: Json
          test_date: string
          total_marks: number
          total_marks_obtained: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          notes?: string | null
          subject_marks: Json
          test_date: string
          total_marks: number
          total_marks_obtained: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          notes?: string | null
          subject_marks?: Json
          test_date?: string
          total_marks?: number
          total_marks_obtained?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      payment_transactions: {
        Row: {
          amount_cents: number
          created_at: string | null
          currency: string
          description: string | null
          dodo_payment_id: string | null
          dodo_subscription_id: string | null
          id: string
          metadata: Json | null
          payment_method: string | null
          processed_at: string | null
          status: string
          subscription_id: string | null
          transaction_type: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          amount_cents: number
          created_at?: string | null
          currency?: string
          description?: string | null
          dodo_payment_id?: string | null
          dodo_subscription_id?: string | null
          id?: string
          metadata?: Json | null
          payment_method?: string | null
          processed_at?: string | null
          status: string
          subscription_id?: string | null
          transaction_type: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          amount_cents?: number
          created_at?: string | null
          currency?: string
          description?: string | null
          dodo_payment_id?: string | null
          dodo_subscription_id?: string | null
          id?: string
          metadata?: Json | null
          payment_method?: string | null
          processed_at?: string | null
          status?: string
          subscription_id?: string | null
          transaction_type?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      subscription_plans: {
        Row: {
          billing_interval: string
          billing_interval_count: number
          created_at: string | null
          currency: string
          description: string | null
          dodo_product_id: string | null
          features: Json | null
          id: string
          is_active: boolean | null
          name: string
          price_cents: number
          updated_at: string | null
        }
        Insert: {
          billing_interval?: string
          billing_interval_count?: number
          created_at?: string | null
          currency?: string
          description?: string | null
          dodo_product_id?: string | null
          features?: Json | null
          id?: string
          is_active?: boolean | null
          name: string
          price_cents: number
          updated_at?: string | null
        }
        Update: {
          billing_interval?: string
          billing_interval_count?: number
          created_at?: string | null
          currency?: string
          description?: string | null
          dodo_product_id?: string | null
          features?: Json | null
          id?: string
          is_active?: boolean | null
          name?: string
          price_cents?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      study_sessions: {
        Row: {
          completed: boolean | null
          created_at: string | null
          date: string
          duration: number
          end_time: string | null
          feedback: string | null
          id: string
          mode: string | null
          notes: string | null
          phase: string | null
          productivity_rating: number | null
          start_time: string
          subject: string | null
          task_name: string | null
          task_type: string | null
          user_id: string
        }
        Insert: {
          completed?: boolean | null
          created_at?: string | null
          date: string
          duration: number
          end_time?: string | null
          feedback?: string | null
          id?: string
          mode?: string | null
          notes?: string | null
          phase?: string | null
          productivity_rating?: number | null
          start_time: string
          subject?: string | null
          task_name?: string | null
          task_type?: string | null
          user_id: string
        }
        Update: {
          completed?: boolean | null
          created_at?: string | null
          date?: string
          duration?: number
          end_time?: string | null
          feedback?: string | null
          id?: string
          mode?: string | null
          notes?: string | null
          phase?: string | null
          productivity_rating?: number | null
          start_time?: string
          subject?: string | null
          task_name?: string | null
          task_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      todos: {
        Row: {
          assignedTo: string | null
          assignedToName: string | null
          assignedToPhotoUrl: string | null
          column_id: string | null
          columnId: string | null
          createdAt: number
          createdBy: string
          description: string | null
          dueDate: number | null
          groupId: string | null
          id: string
          priority: string | null
          title: string
          updatedAt: number
        }
        Insert: {
          assignedTo?: string | null
          assignedToName?: string | null
          assignedToPhotoUrl?: string | null
          column_id?: string | null
          columnId?: string | null
          createdAt: number
          createdBy: string
          description?: string | null
          dueDate?: number | null
          groupId?: string | null
          id: string
          priority?: string | null
          title: string
          updatedAt: number
        }
        Update: {
          assignedTo?: string | null
          assignedToName?: string | null
          assignedToPhotoUrl?: string | null
          column_id?: string | null
          columnId?: string | null
          createdAt?: number
          createdBy?: string
          description?: string | null
          dueDate?: number | null
          groupId?: string | null
          id?: string
          priority?: string | null
          title?: string
          updatedAt?: number
        }
        Relationships: []
      }
      users: {
        Row: {
          backgroundImage: string | null
          bio: string | null
          created_at: string | null
          daily_motivation: string | null
          daily_target: number | null
          day_start_time: number | null
          display_name: string | null
          displayName: string | null
          email: string
          id: string
          last_login: string | null
          lastLogin: string | null
          location: string | null
          member_since: string | null
          mock_tests_data: Json | null
          mockTests: Json | null
          photo_url: string | null
          photoURL: string | null
          profile_views: number | null
          progress: Json | null
          stats: Json | null
          study_sessions_data: Json | null
          studySessions: Json | null
          uid: string | null
          updated_at: string | null
          username: string | null
          welcome_email_sent: boolean | null
          welcomeEmailSent: boolean | null
        }
        Insert: {
          backgroundImage?: string | null
          bio?: string | null
          created_at?: string | null
          daily_motivation?: string | null
          daily_target?: number | null
          day_start_time?: number | null
          display_name?: string | null
          displayName?: string | null
          email: string
          id: string
          last_login?: string | null
          lastLogin?: string | null
          location?: string | null
          member_since?: string | null
          mock_tests_data?: Json | null
          mockTests?: Json | null
          photo_url?: string | null
          photoURL?: string | null
          profile_views?: number | null
          progress?: Json | null
          stats?: Json | null
          study_sessions_data?: Json | null
          studySessions?: Json | null
          uid?: string | null
          updated_at?: string | null
          username?: string | null
          welcome_email_sent?: boolean | null
          welcomeEmailSent?: boolean | null
        }
        Update: {
          backgroundImage?: string | null
          bio?: string | null
          created_at?: string | null
          daily_motivation?: string | null
          daily_target?: number | null
          day_start_time?: number | null
          display_name?: string | null
          displayName?: string | null
          email?: string
          id?: string
          last_login?: string | null
          lastLogin?: string | null
          location?: string | null
          member_since?: string | null
          mock_tests_data?: Json | null
          mockTests?: Json | null
          photo_url?: string | null
          photoURL?: string | null
          profile_views?: number | null
          progress?: Json | null
          stats?: Json | null
          study_sessions_data?: Json | null
          studySessions?: Json | null
          uid?: string | null
          updated_at?: string | null
          username?: string | null
          welcome_email_sent?: boolean | null
          welcomeEmailSent?: boolean | null
        }
        Relationships: []
      }
      user_subscriptions: {
        Row: {
          cancel_at_period_end: boolean | null
          canceled_at: string | null
          created_at: string | null
          current_period_end: string | null
          current_period_start: string | null
          dodo_customer_id: string | null
          dodo_subscription_id: string | null
          id: string
          metadata: Json | null
          status: string
          subscription_plan_id: string | null
          trial_end: string | null
          trial_start: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          cancel_at_period_end?: boolean | null
          canceled_at?: string | null
          created_at?: string | null
          current_period_end?: string | null
          current_period_start?: string | null
          dodo_customer_id?: string | null
          dodo_subscription_id?: string | null
          id?: string
          metadata?: Json | null
          status?: string
          subscription_plan_id?: string | null
          trial_end?: string | null
          trial_start?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          cancel_at_period_end?: boolean | null
          canceled_at?: string | null
          created_at?: string | null
          current_period_end?: string | null
          current_period_start?: string | null
          dodo_customer_id?: string | null
          dodo_subscription_id?: string | null
          id?: string
          metadata?: Json | null
          status?: string
          subscription_plan_id?: string | null
          trial_end?: string | null
          trial_start?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      userSubjects: {
        Row: {
          color: string | null
          createdAt: string | null
          id: string
          name: string
          userId: string
        }
        Insert: {
          color?: string | null
          createdAt?: string | null
          id: string
          name: string
          userId: string
        }
        Update: {
          color?: string | null
          createdAt?: string | null
          id?: string
          name?: string
          userId?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      increment_view_count: {
        Args: { chat_id: string }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  DefaultSchemaCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends DefaultSchemaCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = DefaultSchemaCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : DefaultSchemaCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][DefaultSchemaCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
