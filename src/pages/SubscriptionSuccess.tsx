import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Crown, ArrowRight, Sparkles } from 'lucide-react';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { useToast } from '@/components/ui/use-toast';
import useDocumentTitle from '@/hooks/useDocumentTitle';

const SubscriptionSuccess: React.FC = () => {
  useDocumentTitle('Welcome to Premium - IsotopeAI');
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { refreshSubscription } = useSubscription();
  const { toast } = useToast();

  useEffect(() => {
    // Refresh subscription data when user lands on success page
    const refreshData = async () => {
      try {
        await refreshSubscription();
        toast({
          title: 'Welcome to Premium!',
          description: 'Your subscription has been activated successfully.',
        });
      } catch (error) {
        console.error('Error refreshing subscription:', error);
      }
    };

    refreshData();
  }, [refreshSubscription, toast]);

  const features = [
    'Unlimited AI Chat Sessions',
    'Advanced Analytics & Insights',
    'Productivity Tools & Timers',
    'Group Collaboration Features',
    'Mock Tests & Analysis',
    'Priority Customer Support',
    'Offline Access (PWA)',
    'Early Access to New Features',
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
          <CardHeader className="text-center pb-6">
            <div className="flex items-center justify-center mb-6">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-12 w-12 text-white" />
                </div>
                <div className="absolute -top-2 -right-2">
                  <Crown className="h-8 w-8 text-yellow-500" />
                </div>
                <div className="absolute -bottom-1 -left-1">
                  <Sparkles className="h-6 w-6 text-purple-500" />
                </div>
              </div>
            </div>
            
            <CardTitle className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-2">
              Welcome to IsotopeAI Premium!
            </CardTitle>
            
            <CardDescription className="text-lg text-gray-600 dark:text-gray-300">
              Your subscription has been activated successfully. You now have access to all premium features!
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                <Crown className="h-5 w-5 text-yellow-500" />
                Your Premium Features
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {feature}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                🎉 Special Launch Offer Active
              </h4>
              <p className="text-sm text-blue-800 dark:text-blue-200">
                You've secured our exclusive launch pricing of $6.69/year - that's 90% off! 
                This rate is locked in for as long as you maintain your subscription.
              </p>
            </div>

            <div className="space-y-3">
              <Button
                onClick={() => navigate('/ai')}
                className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold py-3"
                size="lg"
              >
                Start Using Premium Features
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
              
              <Button
                onClick={() => navigate('/subscription/manage')}
                variant="outline"
                className="w-full"
              >
                Manage Subscription
              </Button>
            </div>

            <div className="text-center space-y-2 pt-4 border-t">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Need help getting started?
              </p>
              <Button
                onClick={() => navigate('/help')}
                variant="link"
                className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              >
                View Getting Started Guide
              </Button>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Questions about your subscription?{' '}
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  Contact our support team
                </a>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SubscriptionSuccess;
