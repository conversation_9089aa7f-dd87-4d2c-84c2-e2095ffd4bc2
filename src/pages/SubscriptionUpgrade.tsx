import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { Crown, ArrowLeft, CreditCard, Shield, Zap } from 'lucide-react';
import { formatPrice } from '@/utils/dodoPayments';
import useDocumentTitle from '@/hooks/useDocumentTitle';

interface BillingForm {
  street: string;
  city: string;
  state: string;
  zipcode: string;
  country: string;
}

const SubscriptionUpgrade: React.FC = () => {
  useDocumentTitle('Upgrade to Premium - IsotopeAI');
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useSupabaseAuth();
  const { availablePlans, createSubscription, isLoading } = useSubscription();
  
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const [billingForm, setBillingForm] = useState<BillingForm>({
    street: '',
    city: '',
    state: '',
    zipcode: '',
    country: 'US',
  });
  const [isProcessing, setIsProcessing] = useState(false);

  const handleInputChange = (field: keyof BillingForm, value: string) => {
    setBillingForm(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = (): boolean => {
    if (!selectedPlan) {
      toast({
        title: 'Plan Required',
        description: 'Please select a subscription plan.',
        variant: 'destructive',
      });
      return false;
    }

    const requiredFields: (keyof BillingForm)[] = ['street', 'city', 'state', 'zipcode', 'country'];
    const missingFields = requiredFields.filter(field => !billingForm[field].trim());

    if (missingFields.length > 0) {
      toast({
        title: 'Billing Information Required',
        description: 'Please fill in all billing address fields.',
        variant: 'destructive',
      });
      return false;
    }

    return true;
  };

  const handleUpgrade = async () => {
    if (!validateForm()) return;

    try {
      setIsProcessing(true);

      const paymentLink = await createSubscription(selectedPlan, billingForm);
      
      // Redirect to Dodo Payments checkout
      window.location.href = paymentLink;
    } catch (error) {
      console.error('Subscription creation failed:', error);
      toast({
        title: 'Upgrade Failed',
        description: error instanceof Error ? error.message : 'Failed to create subscription. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const selectedPlanData = availablePlans.find(plan => plan.id === selectedPlan);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(-1)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Upgrade to Premium
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Unlock all features and accelerate your learning
              </p>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Plan Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Crown className="h-5 w-5 text-yellow-500" />
                  Choose Your Plan
                </CardTitle>
                <CardDescription>
                  Select the plan that best fits your needs
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {availablePlans.map((plan) => (
                  <div
                    key={plan.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedPlan === plan.id
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                    onClick={() => setSelectedPlan(plan.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold">{plan.name}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {plan.description}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold">
                          {formatPrice(plan.price_cents, plan.currency)}
                        </div>
                        <div className="text-sm text-gray-500">
                          per {plan.billing_interval}
                        </div>
                      </div>
                    </div>
                    
                    {plan.features && (
                      <div className="mt-3 flex flex-wrap gap-2">
                        {(plan.features as string[]).map((feature, index) => (
                          <span
                            key={index}
                            className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Billing Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Billing Information
                </CardTitle>
                <CardDescription>
                  Enter your billing address for payment processing
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="street">Street Address</Label>
                  <Input
                    id="street"
                    value={billingForm.street}
                    onChange={(e) => handleInputChange('street', e.target.value)}
                    placeholder="123 Main Street"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={billingForm.city}
                      onChange={(e) => handleInputChange('city', e.target.value)}
                      placeholder="New York"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="state">State</Label>
                    <Input
                      id="state"
                      value={billingForm.state}
                      onChange={(e) => handleInputChange('state', e.target.value)}
                      placeholder="NY"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="zipcode">ZIP Code</Label>
                    <Input
                      id="zipcode"
                      value={billingForm.zipcode}
                      onChange={(e) => handleInputChange('zipcode', e.target.value)}
                      placeholder="10001"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="country">Country</Label>
                    <Select value={billingForm.country} onValueChange={(value) => handleInputChange('country', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="US">United States</SelectItem>
                        <SelectItem value="CA">Canada</SelectItem>
                        <SelectItem value="GB">United Kingdom</SelectItem>
                        <SelectItem value="AU">Australia</SelectItem>
                        <SelectItem value="IN">India</SelectItem>
                        {/* Add more countries as needed */}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {selectedPlanData && (
                  <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <h4 className="font-semibold mb-2">Order Summary</h4>
                    <div className="flex justify-between items-center">
                      <span>{selectedPlanData.name}</span>
                      <span className="font-semibold">
                        {formatPrice(selectedPlanData.price_cents, selectedPlanData.currency)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                      Billed {selectedPlanData.billing_interval}ly
                    </div>
                  </div>
                )}

                <Button
                  onClick={handleUpgrade}
                  disabled={!selectedPlan || isProcessing}
                  className="w-full mt-6"
                  size="lg"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Zap className="h-4 w-4 mr-2" />
                      Upgrade Now
                    </>
                  )}
                </Button>

                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300 mt-4">
                  <Shield className="h-4 w-4" />
                  <span>Secure payment powered by Dodo Payments</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionUpgrade;
