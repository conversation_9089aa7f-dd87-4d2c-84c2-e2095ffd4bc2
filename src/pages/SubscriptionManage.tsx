import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { 
  Crown, 
  ArrowLeft, 
  Calendar, 
  CreditCard, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  ExternalLink
} from 'lucide-react';
import { formatPrice } from '@/utils/dodoPaymentsSimple';
import useDocumentTitle from '@/hooks/useDocumentTitle';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

const SubscriptionManage: React.FC = () => {
  useDocumentTitle('Manage Subscription - IsotopeAI');
  const navigate = useNavigate();
  const { toast } = useToast();
  const { 
    subscription, 
    subscriptionPlan, 
    isActive, 
    isExpired, 
    isPending, 
    isCancelled,
    cancelSubscription, 
    refreshSubscription,
    isLoading 
  } = useSubscription();
  
  const [isCancelling, setIsCancelling] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleCancelSubscription = async () => {
    try {
      setIsCancelling(true);
      await cancelSubscription();
      toast({
        title: 'Subscription Cancelled',
        description: 'Your subscription has been cancelled successfully.',
      });
    } catch (error) {
      toast({
        title: 'Cancellation Failed',
        description: error instanceof Error ? error.message : 'Failed to cancel subscription.',
        variant: 'destructive',
      });
    } finally {
      setIsCancelling(false);
    }
  };

  const handleRefreshSubscription = async () => {
    try {
      setIsRefreshing(true);
      await refreshSubscription();
      toast({
        title: 'Subscription Updated',
        description: 'Your subscription information has been refreshed.',
      });
    } catch (error) {
      toast({
        title: 'Refresh Failed',
        description: 'Failed to refresh subscription information.',
        variant: 'destructive',
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusBadge = () => {
    if (isActive) {
      return <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Active</Badge>;
    }
    if (isPending) {
      return <Badge variant="outline">Pending</Badge>;
    }
    if (isCancelled) {
      return <Badge variant="secondary">Cancelled</Badge>;
    }
    if (isExpired) {
      return <Badge variant="destructive">Expired</Badge>;
    }
    return <Badge variant="outline">{subscription?.status || 'Unknown'}</Badge>;
  };

  const getStatusIcon = () => {
    if (isActive) return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (isPending) return <RefreshCw className="h-5 w-5 text-yellow-500" />;
    if (isCancelled || isExpired) return <XCircle className="h-5 w-5 text-red-500" />;
    return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center">
            <Card>
              <CardHeader>
                <CardTitle>No Active Subscription</CardTitle>
                <CardDescription>
                  You don't have an active subscription. Upgrade to premium to access all features.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button onClick={() => navigate('/subscription/upgrade')}>
                  <Crown className="h-4 w-4 mr-2" />
                  Upgrade to Premium
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(-1)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Manage Subscription</h1>
              <p className="text-gray-600 dark:text-gray-300">
                View and manage your IsotopeAI subscription
              </p>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Subscription Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Crown className="h-5 w-5 text-yellow-500" />
                  Subscription Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Status</span>
                  <div className="flex items-center gap-2">
                    {getStatusIcon()}
                    {getStatusBadge()}
                  </div>
                </div>

                {subscriptionPlan && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Plan</span>
                      <span>{subscriptionPlan.name}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="font-medium">Price</span>
                      <span className="font-semibold">
                        {formatPrice(subscriptionPlan.price_cents, subscriptionPlan.currency)}
                        /{subscriptionPlan.billing_interval}
                      </span>
                    </div>
                  </>
                )}

                {subscription.current_period_start && (
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Current Period</span>
                    <span className="text-sm">
                      {new Date(subscription.current_period_start).toLocaleDateString()} - {' '}
                      {subscription.current_period_end 
                        ? new Date(subscription.current_period_end).toLocaleDateString()
                        : 'N/A'
                      }
                    </span>
                  </div>
                )}

                {subscription.dodo_subscription_id && (
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Subscription ID</span>
                    <span className="text-sm font-mono">
                      {subscription.dodo_subscription_id.slice(0, 8)}...
                    </span>
                  </div>
                )}

                <div className="pt-4 border-t">
                  <Button
                    onClick={handleRefreshSubscription}
                    disabled={isRefreshing}
                    variant="outline"
                    className="w-full"
                  >
                    {isRefreshing ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Refreshing...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh Status
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Subscription Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {isActive && (
                  <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                    <div className="flex items-center gap-2 text-green-800 dark:text-green-200 mb-2">
                      <CheckCircle className="h-4 w-4" />
                      <span className="font-semibold">Subscription Active</span>
                    </div>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      Your premium subscription is active and all features are available.
                    </p>
                  </div>
                )}

                {(isExpired || isCancelled) && (
                  <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                    <div className="flex items-center gap-2 text-red-800 dark:text-red-200 mb-2">
                      <XCircle className="h-4 w-4" />
                      <span className="font-semibold">
                        {isExpired ? 'Subscription Expired' : 'Subscription Cancelled'}
                      </span>
                    </div>
                    <p className="text-sm text-red-700 dark:text-red-300 mb-3">
                      {isExpired 
                        ? 'Your subscription has expired. Renew to continue using premium features.'
                        : 'Your subscription has been cancelled. You can reactivate it anytime.'
                      }
                    </p>
                    <Button
                      onClick={() => navigate('/subscription/upgrade')}
                      className="w-full"
                    >
                      <Crown className="h-4 w-4 mr-2" />
                      {isExpired ? 'Renew Subscription' : 'Reactivate Subscription'}
                    </Button>
                  </div>
                )}

                {isPending && (
                  <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200 mb-2">
                      <RefreshCw className="h-4 w-4" />
                      <span className="font-semibold">Payment Pending</span>
                    </div>
                    <p className="text-sm text-yellow-700 dark:text-yellow-300">
                      Your subscription is being processed. This usually takes a few minutes.
                    </p>
                  </div>
                )}

                <div className="space-y-3">
                  <Button
                    onClick={() => window.open('https://app.dodopayments.com/customer-portal', '_blank')}
                    variant="outline"
                    className="w-full"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Customer Portal
                  </Button>

                  {isActive && (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="destructive" className="w-full">
                          <XCircle className="h-4 w-4 mr-2" />
                          Cancel Subscription
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Cancel Subscription</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to cancel your subscription? You'll lose access to all premium features at the end of your current billing period.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Keep Subscription</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={handleCancelSubscription}
                            disabled={isCancelling}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            {isCancelling ? 'Cancelling...' : 'Cancel Subscription'}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  )}
                </div>

                <div className="pt-4 border-t text-center">
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    Need help with your subscription?
                  </p>
                  <Button
                    onClick={() => window.open('mailto:<EMAIL>', '_blank')}
                    variant="link"
                    className="text-blue-600 hover:text-blue-700 dark:text-blue-400"
                  >
                    Contact Support
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionManage;
