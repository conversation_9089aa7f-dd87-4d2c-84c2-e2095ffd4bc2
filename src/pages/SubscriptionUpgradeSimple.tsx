import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { Crown, ArrowLeft, Zap, CheckCircle, Star } from 'lucide-react';
import { formatPrice } from '@/utils/dodoPaymentsSimple';
import useDocumentTitle from '@/hooks/useDocumentTitle';
import { SubscriptionDebug } from '@/components/subscription/SubscriptionDebug';
import { SetupInstructions } from '@/components/subscription/SetupInstructions';
import { validateDodoConfig } from '@/config/dodoConfig';

const SubscriptionUpgradeSimple: React.FC = () => {
  useDocumentTitle('Upgrade to Premium - IsotopeAI');
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useSupabaseAuth();
  const { availablePlans, createSubscription, isLoading } = useSubscription();
  
  const [isProcessing, setIsProcessing] = useState(false);
  const configValidation = validateDodoConfig();

  // Simple billing address - just country for tax purposes
  const defaultBillingAddress = {
    street: '123 Main St',
    city: 'City',
    state: 'State',
    zipcode: '12345',
    country: 'US', // You can make this dynamic based on user location
  };

  const handleUpgrade = async (planId: string) => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to upgrade your subscription.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsProcessing(true);

      // Create subscription and get payment link
      const paymentLink = await createSubscription(planId, defaultBillingAddress);
      
      // Redirect to Dodo Payments checkout
      window.location.href = paymentLink;
    } catch (error) {
      console.error('Subscription creation failed:', error);
      toast({
        title: 'Upgrade Failed',
        description: error instanceof Error ? error.message : 'Failed to create subscription. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const features = [
    'Unlimited AI Chat Sessions',
    'Advanced Analytics & Progress Tracking',
    'Productivity Tools & Study Timers',
    'Group Collaboration Features',
    'Mock Tests & Detailed Analysis',
    'Priority Customer Support',
    'Offline Access (PWA)',
    'Early Access to New Features',
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(-1)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Upgrade to Premium
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Unlock all features and accelerate your learning
              </p>
            </div>
          </div>

          {/* Setup Instructions - Show if configuration is incomplete */}
          {!configValidation.isValid && (
            <div className="mb-8">
              <SetupInstructions />
            </div>
          )}

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Plan Selection */}
            <Card className="relative overflow-hidden">
              <div className="absolute top-0 right-0 bg-gradient-to-l from-yellow-400 to-orange-500 text-white px-4 py-1 text-sm font-semibold">
                🎉 Launch Offer
              </div>
              <CardHeader className="pt-8">
                <CardTitle className="flex items-center gap-2">
                  <Crown className="h-6 w-6 text-yellow-500" />
                  IsotopeAI Premium
                </CardTitle>
                <CardDescription>
                  Everything you need to excel in your studies
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {availablePlans.map((plan) => (
                  <div key={plan.id} className="text-center">
                    <div className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                      {formatPrice(plan.price_cents, plan.currency)}
                    </div>
                    <div className="text-lg text-gray-600 dark:text-gray-300 mb-1">
                      per {plan.billing_interval}
                    </div>
                    <div className="text-sm text-green-600 dark:text-green-400 font-semibold mb-6">
                      90% OFF - Limited Time!
                    </div>
                    
                    <Button
                      onClick={() => handleUpgrade(plan.id)}
                      disabled={isProcessing}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 text-lg"
                      size="lg"
                    >
                      {isProcessing ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <Zap className="h-5 w-5 mr-2" />
                          Upgrade Now
                        </>
                      )}
                    </Button>
                  </div>
                ))}

                <div className="text-center text-sm text-gray-500 dark:text-gray-400 space-y-1">
                  <p>✅ 30-day money-back guarantee</p>
                  <p>✅ Cancel anytime</p>
                  <p>✅ Secure payment by Dodo Payments</p>
                </div>
              </CardContent>
            </Card>

            {/* Features */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-500" />
                  What You Get
                </CardTitle>
                <CardDescription>
                  All premium features included
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {features.map((feature, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                    </div>
                  ))}
                </div>

                <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                    🚀 Perfect for Students
                  </h4>
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    Whether you're preparing for JEE, NEET, BITSAT, or just want to excel in Physics, 
                    Chemistry, and Mathematics - IsotopeAI Premium gives you everything you need to succeed.
                  </p>
                </div>

                <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                  <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">
                    💡 Why Students Love IsotopeAI
                  </h4>
                  <ul className="text-sm text-green-800 dark:text-green-200 space-y-1">
                    <li>• Get instant help with complex problems</li>
                    <li>• Track your study progress scientifically</li>
                    <li>• Collaborate with classmates effectively</li>
                    <li>• Practice with unlimited mock tests</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* FAQ Section */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-1">Can I cancel anytime?</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Yes! You can cancel your subscription at any time from your account settings. 
                  You'll continue to have access until the end of your billing period.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-1">Is my payment secure?</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Absolutely! All payments are processed securely by Dodo Payments with industry-standard encryption. 
                  We never store your payment information.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-1">What if I'm not satisfied?</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  We offer a 30-day money-back guarantee. If you're not completely satisfied, 
                  contact us for a full refund.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Debug Panel - Development Only */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-8">
              <SubscriptionDebug />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SubscriptionUpgradeSimple;
